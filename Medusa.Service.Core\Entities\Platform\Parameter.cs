using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Core.Entities.Platform
{
    /// <summary>
    /// 变量
    /// </summary>
    [EntityTable("Parameters", "变量表")]
    public class Parameter
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ParameterId { get; set; }

        /// <summary>
        /// 变量编码
        /// </summary>
        [EntityColumn(ColumnDescription = "编码", ColumnDataType = "varchar(255)", IsNullable = true)]
        public string Code { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [EntityColumn(ColumnDescription = "名称", ColumnDataType = "varchar(255)", IsNullable = true)]
        public string Name { get; set; }

        /// <summary>
        /// 值
        /// </summary>
        [EntityColumn(ColumnDescription = "值", ColumnDataType = "varchar(255)", IsNullable = true)]
        public string Value { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [EntityColumn(ColumnDescription = "描述", ColumnDataType = "varchar(255)", IsNullable = true)]
        public string Description { get; set; }
    }
}
