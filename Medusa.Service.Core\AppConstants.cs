using System.Threading;
using Medusa.Service.Core.Dtos;

namespace Medusa.Service.Core
{
    /// <summary>
    /// 应用常量
    /// </summary>
    public static class AppConstants
    {
        /// <summary>
        /// 头信息中的User对象
        /// </summary>
        public const string HeaderUser = "current_user";

        /// <summary>
        /// 应用版本
        /// </summary>
        public const string AppVersion = "Version";

        /// <summary>
        /// 应用默认语言
        /// </summary>
        public const string I18nDefaultLang = "zh-CN";

        /// <summary>
        /// 当前项目名称
        /// </summary>
        public const string ProjectName = "Medusa.Service.Bpa.Entrance";

        /// <summary>
        /// 当前登录人（由 UserStateMiddleware 中间件写入）
        /// 该对象只有在 Http 接口调用时起效，请勿使用在事件接口调用的方法中！
        /// </summary>
        public static AsyncLocal<HeaderUser> CurrentUser { get; } = new AsyncLocal<HeaderUser>();
    }
}
