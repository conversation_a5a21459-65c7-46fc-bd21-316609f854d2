using MT.Enterprise.Core.ORM;
using SqlSugar;

namespace Medusa.Service.Core.ORM
{
    /// <summary>
    /// 扩展ISugarQueryable
    /// </summary>
    public static class MyQueryable
    {
        /// <summary>
        /// 数据权限的扩展
        /// </summary>
        /// <typeparam name="T">表对象</typeparam>
        /// <param name="queryable">查询结构</param>
        /// <param name="tableNickName">表短名</param>
        /// <param name="dbClient">Platform dbClient</param>
        /// <param name="permissionCode">数据权限Code</param>
        /// <param name="usePermission">是否启用数据权限注入</param>
        /// <returns>注入数据权限后的查询结构</returns>
        public static ISugarQueryable<T> DataPermission<T>(
            this ISugarQueryable<T> queryable,
            string tableNickName = null,
            MultipleClient dbClient = null,
            string permissionCode = null,
            bool usePermission = true)
        {
            return MT.Enterprise.Core.ORM.Queryable.Queryable.DataPermission(
                queryable,
                dbClient == null ? queryable.Context : dbClient.Context,
                GetSystemVariablesValue,
                "platform",
                tableNickName,
                permissionCode,
                usePermission);
        }

        /// <summary>
        /// 数据权限的扩展
        /// </summary>
        /// <param name="queryable">查询结构</param>
        /// <param name="tableNickNames">表短名</param>
        /// <param name="dbClient">Platform dbClient</param>
        /// <param name="permissionCode">数据权限Code</param>
        /// <param name="usePermission">是否启用数据权限注入</param>
        /// <typeparam name="T1">表对象1</typeparam>
        /// <typeparam name="T2">表对象2</typeparam>
        /// <returns>注入数据权限后的查询结构</returns>
        public static ISugarQueryable<T1, T2> DataPermission<T1, T2>(
            this ISugarQueryable<T1, T2> queryable,
            string[] tableNickNames = null,
            MultipleClient dbClient = null,
            string permissionCode = null,
            bool usePermission = true)
        {
            return MT.Enterprise.Core.ORM.Queryable.Queryable.DataPermission(
                queryable,
                dbClient == null ? queryable.Context : dbClient.Context,
                GetSystemVariablesValue,
                "platform",
                tableNickNames,
                permissionCode,
                usePermission);
        }

        /// <summary>
        /// 数据权限的扩展
        /// </summary>
        /// <param name="queryable">查询结构</param>
        /// <param name="tableNickNames">表短名</param>
        /// <param name="dbClient">Platform dbClient</param>
        /// <param name="permissionCode">数据权限Code</param>
        /// <param name="usePermission">是否启用数据权限注入</param>
        /// <typeparam name="T1">表对象1</typeparam>
        /// <typeparam name="T2">表对象2</typeparam>
        /// <typeparam name="T3">表对象3</typeparam>
        /// <returns>注入数据权限后的查询结构</returns>
        public static ISugarQueryable<T1, T2, T3> DataPermission<T1, T2, T3>(
            this ISugarQueryable<T1, T2, T3> queryable,
            string[] tableNickNames = null,
            MultipleClient dbClient = null,
            string permissionCode = null,
            bool usePermission = true)
        {
            return MT.Enterprise.Core.ORM.Queryable.Queryable.DataPermission(
                queryable,
                dbClient == null ? queryable.Context : dbClient.Context,
                GetSystemVariablesValue,
                "platform",
                tableNickNames,
                permissionCode,
                usePermission);
        }

        /// <summary>
        /// 数据权限的扩展
        /// </summary>
        /// <param name="queryable">查询结构</param>
        /// <param name="tableNickNames">表短名</param>
        /// <param name="dbClient">Platform dbClient</param>
        /// <param name="permissionCode">数据权限Code</param>
        /// <param name="usePermission">是否启用数据权限注入</param>
        /// <typeparam name="T1">表对象1</typeparam>
        /// <typeparam name="T2">表对象2</typeparam>
        /// <typeparam name="T3">表对象3</typeparam>
        /// <typeparam name="T4">表对象4</typeparam>
        /// <returns>注入数据权限后的查询结构</returns>
        public static ISugarQueryable<T1, T2, T3, T4> DataPermission<T1, T2, T3, T4>(
            this ISugarQueryable<T1, T2, T3, T4> queryable,
            string[] tableNickNames = null,
            MultipleClient dbClient = null,
            string permissionCode = null,
            bool usePermission = true)
        {
            return MT.Enterprise.Core.ORM.Queryable.Queryable.DataPermission(
                queryable,
                dbClient == null ? queryable.Context : dbClient.Context,
                GetSystemVariablesValue,
                "platform",
                tableNickNames,
                permissionCode,
                usePermission);
        }

        /// <summary>
        /// 数据权限的扩展
        /// </summary>
        /// <param name="queryable">查询结构</param>
        /// <param name="tableNickNames">表短名</param>
        /// <param name="dbClient">Platform dbClient</param>
        /// <param name="permissionCode">数据权限Code</param>
        /// <param name="usePermission">是否启用数据权限注入</param>
        /// <typeparam name="T1">表对象1</typeparam>
        /// <typeparam name="T2">表对象2</typeparam>
        /// <typeparam name="T3">表对象3</typeparam>
        /// <typeparam name="T4">表对象4</typeparam>
        /// <typeparam name="T5">表对象5</typeparam>
        /// <returns>注入数据权限后的查询结构</returns>
        public static ISugarQueryable<T1, T2, T3, T4, T5> DataPermission<T1, T2, T3, T4, T5>(
            this ISugarQueryable<T1, T2, T3, T4, T5> queryable,
            string[] tableNickNames = null,
            MultipleClient dbClient = null,
            string permissionCode = null,
            bool usePermission = true)
        {
            return MT.Enterprise.Core.ORM.Queryable.Queryable.DataPermission(
                queryable,
                dbClient == null ? queryable.Context : dbClient.Context,
                GetSystemVariablesValue,
                "platform",
                tableNickNames,
                permissionCode,
                usePermission);
        }

        /// <summary>
        /// 数据权限的扩展
        /// </summary>
        /// <param name="queryable">查询结构</param>
        /// <param name="tableNickNames">表短名</param>
        /// <param name="dbClient">Platform dbClient</param>
        /// <param name="permissionCode">数据权限Code</param>
        /// <param name="usePermission">是否启用数据权限注入</param>
        /// <typeparam name="T1">表对象1</typeparam>
        /// <typeparam name="T2">表对象2</typeparam>
        /// <typeparam name="T3">表对象3</typeparam>
        /// <typeparam name="T4">表对象4</typeparam>
        /// <typeparam name="T5">表对象5</typeparam>
        /// <typeparam name="T6">表对象6</typeparam>
        /// <returns>注入数据权限后的查询结构</returns>
        public static ISugarQueryable<T1, T2, T3, T4, T5, T6> DataPermission<T1, T2, T3, T4, T5, T6>(
            this ISugarQueryable<T1, T2, T3, T4, T5, T6> queryable,
            string[] tableNickNames = null,
            MultipleClient dbClient = null,
            string permissionCode = null,
            bool usePermission = true)
        {
            return MT.Enterprise.Core.ORM.Queryable.Queryable.DataPermission(
                queryable,
                dbClient == null ? queryable.Context : dbClient.Context,
                GetSystemVariablesValue,
                "platform",
                tableNickNames,
                permissionCode,
                usePermission);
        }

        /// <summary>
        /// 数据权限的扩展
        /// </summary>
        /// <param name="queryable">查询结构</param>
        /// <param name="tableNickNames">表短名</param>
        /// <param name="dbClient">Platform dbClient</param>
        /// <param name="permissionCode">数据权限Code</param>
        /// <param name="usePermission">是否启用数据权限注入</param>
        /// <typeparam name="T1">表对象1</typeparam>
        /// <typeparam name="T2">表对象2</typeparam>
        /// <typeparam name="T3">表对象3</typeparam>
        /// <typeparam name="T4">表对象4</typeparam>
        /// <typeparam name="T5">表对象5</typeparam>
        /// <typeparam name="T6">表对象6</typeparam>
        /// <typeparam name="T7">表对象7</typeparam>
        /// <returns>注入数据权限后的查询结构</returns>
        public static ISugarQueryable<T1, T2, T3, T4, T5, T6, T7> DataPermission<T1, T2, T3, T4, T5, T6, T7>(
            this ISugarQueryable<T1, T2, T3, T4, T5, T6, T7> queryable,
            string[] tableNickNames = null,
            MultipleClient dbClient = null,
            string permissionCode = null,
            bool usePermission = true)
        {
            return MT.Enterprise.Core.ORM.Queryable.Queryable.DataPermission(
                queryable,
                dbClient == null ? queryable.Context : dbClient.Context,
                GetSystemVariablesValue,
                "platform",
                tableNickNames,
                permissionCode,
                usePermission);
        }

        /// <summary>
        /// 数据权限的扩展
        /// </summary>
        /// <param name="queryable">查询结构</param>
        /// <param name="tableNickNames">表短名</param>
        /// <param name="dbClient">Platform dbClient</param>
        /// <param name="permissionCode">数据权限Code</param>
        /// <param name="usePermission">是否启用数据权限注入</param>
        /// <typeparam name="T1">表对象1</typeparam>
        /// <typeparam name="T2">表对象2</typeparam>
        /// <typeparam name="T3">表对象3</typeparam>
        /// <typeparam name="T4">表对象4</typeparam>
        /// <typeparam name="T5">表对象5</typeparam>
        /// <typeparam name="T6">表对象6</typeparam>
        /// <typeparam name="T7">表对象7</typeparam>
        /// <typeparam name="T8">表对象8</typeparam>
        /// <returns>注入数据权限后的查询结构</returns>
        public static ISugarQueryable<T1, T2, T3, T4, T5, T6, T7, T8> DataPermission<T1, T2, T3, T4, T5, T6, T7, T8>(
            this ISugarQueryable<T1, T2, T3, T4, T5, T6, T7, T8> queryable,
            string[] tableNickNames = null,
            MultipleClient dbClient = null,
            string permissionCode = null,
            bool usePermission = true)
        {
            return MT.Enterprise.Core.ORM.Queryable.Queryable.DataPermission(
                queryable,
                dbClient == null ? queryable.Context : dbClient.Context,
                GetSystemVariablesValue,
                "platform",
                tableNickNames,
                permissionCode,
                usePermission);
        }

        /// <summary>
        /// 数据权限的扩展
        /// </summary>
        /// <param name="queryable">查询结构</param>
        /// <param name="tableNickNames">表短名</param>
        /// <param name="dbClient">Platform dbClient</param>
        /// <param name="permissionCode">数据权限Code</param>
        /// <param name="usePermission">是否启用数据权限注入</param>
        /// <typeparam name="T1">表对象1</typeparam>
        /// <typeparam name="T2">表对象2</typeparam>
        /// <typeparam name="T3">表对象3</typeparam>
        /// <typeparam name="T4">表对象4</typeparam>
        /// <typeparam name="T5">表对象5</typeparam>
        /// <typeparam name="T6">表对象6</typeparam>
        /// <typeparam name="T7">表对象7</typeparam>
        /// <typeparam name="T8">表对象8</typeparam>
        /// <typeparam name="T9">表对象9</typeparam>
        /// <returns>注入数据权限后的查询结构</returns>
        public static ISugarQueryable<T1, T2, T3, T4, T5, T6, T7, T8, T9> DataPermission<T1, T2, T3, T4, T5, T6, T7, T8, T9>(
            this ISugarQueryable<T1, T2, T3, T4, T5, T6, T7, T8, T9> queryable,
            string[] tableNickNames = null,
            MultipleClient dbClient = null,
            string permissionCode = null,
            bool usePermission = true)
        {
            return MT.Enterprise.Core.ORM.Queryable.Queryable.DataPermission(
                queryable,
                dbClient == null ? queryable.Context : dbClient.Context,
                GetSystemVariablesValue,
                "platform",
                tableNickNames,
                permissionCode,
                usePermission);
        }

        /// <summary>
        /// 数据权限的扩展
        /// </summary>
        /// <param name="queryable">查询结构</param>
        /// <param name="tableNickNames">表短名</param>
        /// <param name="dbClient">Platform dbClient</param>
        /// <param name="permissionCode">数据权限Code</param>
        /// <param name="usePermission">是否启用数据权限注入</param>
        /// <typeparam name="T1">表对象1</typeparam>
        /// <typeparam name="T2">表对象2</typeparam>
        /// <typeparam name="T3">表对象3</typeparam>
        /// <typeparam name="T4">表对象4</typeparam>
        /// <typeparam name="T5">表对象5</typeparam>
        /// <typeparam name="T6">表对象6</typeparam>
        /// <typeparam name="T7">表对象7</typeparam>
        /// <typeparam name="T8">表对象8</typeparam>
        /// <typeparam name="T9">表对象9</typeparam>
        /// <typeparam name="T10">表对象10</typeparam>
        /// <returns>注入数据权限后的查询结构</returns>
        public static ISugarQueryable<T1, T2, T3, T4, T5, T6, T7, T8, T9, T10> DataPermission<T1, T2, T3, T4, T5, T6, T7, T8, T9, T10>(
            this ISugarQueryable<T1, T2, T3, T4, T5, T6, T7, T8, T9, T10> queryable,
            string[] tableNickNames = null,
            MultipleClient dbClient = null,
            string permissionCode = null,
            bool usePermission = true)
        {
            return MT.Enterprise.Core.ORM.Queryable.Queryable.DataPermission(
                queryable,
                dbClient == null ? queryable.Context : dbClient.Context,
                GetSystemVariablesValue,
                "platform",
                tableNickNames,
                permissionCode,
                usePermission);
        }

        /// <summary>
        /// 数据权限的扩展
        /// </summary>
        /// <param name="queryable">查询结构</param>
        /// <param name="tableNickNames">表短名</param>
        /// <param name="dbClient">Platform dbClient</param>
        /// <param name="permissionCode">数据权限Code</param>
        /// <param name="usePermission">是否启用数据权限注入</param>
        /// <typeparam name="T1">表对象1</typeparam>
        /// <typeparam name="T2">表对象2</typeparam>
        /// <typeparam name="T3">表对象3</typeparam>
        /// <typeparam name="T4">表对象4</typeparam>
        /// <typeparam name="T5">表对象5</typeparam>
        /// <typeparam name="T6">表对象6</typeparam>
        /// <typeparam name="T7">表对象7</typeparam>
        /// <typeparam name="T8">表对象8</typeparam>
        /// <typeparam name="T9">表对象9</typeparam>
        /// <typeparam name="T10">表对象10</typeparam>
        /// <typeparam name="T11">表对象11</typeparam>
        /// <returns>注入数据权限后的查询结构</returns>
        public static ISugarQueryable<T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11> DataPermission<T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11>(
            this ISugarQueryable<T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11> queryable,
            string[] tableNickNames = null,
            MultipleClient dbClient = null,
            string permissionCode = null,
            bool usePermission = true)
        {
            return MT.Enterprise.Core.ORM.Queryable.Queryable.DataPermission(
                queryable,
                dbClient == null ? queryable.Context : dbClient.Context,
                GetSystemVariablesValue,
                "platform",
                tableNickNames,
                permissionCode,
                usePermission);
        }

        /// <summary>
        /// 数据权限的扩展
        /// </summary>
        /// <param name="queryable">查询结构</param>
        /// <param name="tableNickNames">表短名</param>
        /// <param name="dbClient">Platform dbClient</param>
        /// <param name="permissionCode">数据权限Code</param>
        /// <param name="usePermission">是否启用数据权限注入</param>
        /// <typeparam name="T1">表对象1</typeparam>
        /// <typeparam name="T2">表对象2</typeparam>
        /// <typeparam name="T3">表对象3</typeparam>
        /// <typeparam name="T4">表对象4</typeparam>
        /// <typeparam name="T5">表对象5</typeparam>
        /// <typeparam name="T6">表对象6</typeparam>
        /// <typeparam name="T7">表对象7</typeparam>
        /// <typeparam name="T8">表对象8</typeparam>
        /// <typeparam name="T9">表对象9</typeparam>
        /// <typeparam name="T10">表对象10</typeparam>
        /// <typeparam name="T11">表对象11</typeparam>
        /// <typeparam name="T12">表对象12</typeparam>
        /// <returns>注入数据权限后的查询结构</returns>
        public static ISugarQueryable<T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12> DataPermission<T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12>(
            this ISugarQueryable<T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12> queryable,
            string[] tableNickNames = null,
            MultipleClient dbClient = null,
            string permissionCode = null,
            bool usePermission = true)
        {
            return MT.Enterprise.Core.ORM.Queryable.Queryable.DataPermission(
                queryable,
                dbClient == null ? queryable.Context : dbClient.Context,
                GetSystemVariablesValue,
                "platform",
                tableNickNames,
                permissionCode,
                usePermission);
        }

        private static string GetSystemVariablesValue(string variable, SqlSugarProvider dbContext)
        {
            switch (variable)
            {
                case "currentUser":
                    return AppConstants.CurrentUser.Value.Id.ToString();
                /*case "currentDepart":
                    var currentUserId = AppConstants.CurrentUser.Value.Id;
                    return dbContext.Queryable<User, Organization>((t1, t2) => new object[]
                        {
                            JoinType.Left, t1.FullPathCode == t2.FullPathCode
                        }).Where((t1, t2) => t1.UserId == currentUserId)
                        .Select((t1, t2) => t2.OrganizationId.ToString()).First();*/
                default:
                    return string.Empty;
            }
        }
    }
}