﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Application.SyncUsers.Dtos.SaiWu
{
    /// <summary>
    /// 赛伍主数据Token
    /// </summary>
    public class AccessTokenDto
    {
        /// <summary>
        /// 令牌
        /// </summary>
        [JsonProperty(PropertyName = "access_token")]
        public string AccessToken { get; set; }

        /// <summary>
        /// 超时时间，单位秒
        /// </summary>
        [JsonProperty(PropertyName = "expires_in")]

        public int ExpiresIn { get; set; }

        /// <summary>
        /// 令牌类型
        /// </summary>
        [JsonProperty(PropertyName = "token_type")]
        public string TokenType { get; set; }

        /// <summary>
        /// 范围，未用到
        /// </summary>
        [JsonProperty(PropertyName = "scope")]
        public string Scope { get; set; }
    }
}
