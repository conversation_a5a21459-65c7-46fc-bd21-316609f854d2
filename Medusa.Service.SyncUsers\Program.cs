using System;
using System.IO;
using System.Linq;
using System.Text;
using Microsoft.Extensions.Configuration;
using AspectCore.Extensions.DependencyInjection;
using AspectCore.Configuration;
using MT.Enterprise.Core.ORM;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Utils.Extensions;
using Medusa.Service.Core.Dtos;
using Newtonsoft.Json;
using MT.Enterprise.Core;
using MT.Enterprise.Core.Log;
using Medusa.Service.Application;
using Medusa.Service.Application.SyncUsers;
using Microsoft.Extensions.Hosting;
using Nacos;
using Newtonsoft.Json.Linq;
using Microsoft.Extensions.Caching.Memory;

namespace Medusa.Service.SyncUsers
{
    /// <summary>
    /// Programs.
    /// </summary>
    public static class Program
    {
        private static IServiceProvider provider;
        //private static ISyncUserService _syncUserService;
        private static SyncUserContext _syncUserContext;
        //private static JobAppSettingsDto jobAppSettings;
        private static JObject jobAppSettings;

        /// <summary>
        /// Main.
        /// </summary>
        /// <param name="args">args.</param>
        public static void Main(string[] args)
        {
            InitConfig();


            var _appSettingsDto = provider.GetService<IMemoryCache>().Get<JObject>("AppSettings");

            _syncUserContext = provider.GetService<SyncUserContext>();
            _syncUserContext.GetImplementation(jobAppSettings["SystemName"].ToString()).SyncOrgAndUsersAsync();
        }

        /// <summary>
        /// 初始化.
        /// </summary>
        private static void InitConfig()
        {
            Console.WriteLine($"Start init, time is:{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");
            var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            Console.WriteLine($"Enviroment is:{environmentName}");

            var configuration = new ConfigurationBuilder()
                .SetBasePath(AppContext.BaseDirectory)
                .AddJsonFile("appsettings.json", false, true)
                .AddJsonFile($"appsettings.{environmentName}.json", true, true)
                .Build();

            var nacosConfig = configuration.GetSection("Nacos");

            Console.WriteLine("=======> Here register nacos.");

            Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder()
                .ConfigureServices((hostContext, services) =>
                {
                    services.ConfigureDynamicProxy(config =>
                    {
                        config.Interceptors.AddTyped<TransactionScopeAttribute>();
                    });
                    services.AddMemoryCache();
                    services.AddNacos(configure =>
                    {
                        configure.DefaultTimeOut = nacosConfig["DefaultTimeOut"].ToOurInt();
                        configure.ServerAddresses = nacosConfig.GetSection("ServerAddresses").Get<string[]>().ToList();
                        configure.Namespace = nacosConfig["Namespace"];
                        configure.ListenInterval = nacosConfig["ListenInterval"].ToOurInt();
                    });

                    Console.WriteLine("=======> Here add nacos.");

                    jobAppSettings = CreateRegister(services, nacosConfig, configuration);




                    //Microsoft.Extensions.Options.IPo
                    //IMemoryCache cache = new MemoryCache()
                    //var entryOptions = new MemoryCacheEntryOptions().SetPriority(CacheItemPriority.NeverRemove);
                    //cache.Set("AppSettings", jobAppSettings, entryOptions);

                    //app.UseUserState();
                    //app.UseGatewayConnectorState();
                    //app.UseRequestConnectorState();
                    //app.UseRouting();
                    //this.ExternalConfigure(app, env);

                    Console.WriteLine("=======> Here get job appsettings.");

                    //Enum.TryParse(jobAppSettings.Persistence.Boost.DbType, true, out ConnectionDbType boostDbType);
                    //Enum.TryParse(jobAppSettings.Persistence.SourceFrom.DbType, true, out ConnectionDbType sourceFromDbType);

                    Enum.TryParse(jobAppSettings["Persistence"]?["Boost"]?["DbType"].ToString(), true, out ConnectionDbType boostDbType);
                    Enum.TryParse(jobAppSettings["Persistence"]?["Persistence"]?["DbType"].ToString(), true, out ConnectionDbType sourceFromDbType);

                    Console.WriteLine("=======> Here register db type.");

                    //services.AddSingleton<IConfiguration>(configuration);
                    //services.AddSingleton(cache);

                    // 添加 Core
                    services.AddCore(core =>
                    {
                        core.AddLog(log =>
                        {
                            log.MinimumLevel = NLog.LogLevel.Warn;

                            log.AddTarget(opt =>
                            {
                                opt.Type = PersistentType.MongoDB;
                                //  opt.LogAddress = jobAppSettings.LogSettings.MongoDBContext; // logConfig["MongoDBContext"];
                            });
                        });

                        core.AddORM(orm =>
                        {
                            orm.AddDbContext(opt =>
                            {
                                opt.Name = "boost";
                                opt.ConnectionString = jobAppSettings["Persistence"]?["Boost"]?["ConnectionString"].ToString(); // engineDbConfig["ConnectionString"];
                                opt.ConnectionDbType = boostDbType;
                                opt.IsDefault = true;
                            });

                            //// 兼容性代码，防止有些老项目没有SourceFrom
                            //// Todo，准备兼容SourceFrom2,SourceFrom3,SourceFrom4
                            //if (!string.IsNullOrEmpty(jobAppSettings.Persistence.SourceFrom.ConnectionString))
                            //{
                            //    orm.AddDbContext(opt =>
                            //    {
                            //        opt.Name = "sourcefrom";
                            //        opt.ConnectionString = jobAppSettings.Persistence.SourceFrom.ConnectionString; // engineDbConfig["ConnectionString"];
                            //    opt.ConnectionDbType = sourceFromDbType;
                            //        opt.IsDefault = true;
                            //    });
                            //}
                        });
                    });

                    // 添加引擎上下文
                    services.AddOptions();

                    // AutoMapper 注册
                    //  services.AddMappers(options =>
                    //  {
                    //      options.AddPlatformMappers();
                    // });
                    Console.WriteLine("=======> Here register auto mapper.");

                    services.AddPlatformServices(configuration);

                    Console.WriteLine("=======> Here register platform service.");
                    provider = services.BuildServiceProvider();

                    services.AddAppSettingsServices(jobAppSettings);

                    Console.WriteLine("=======> Here register provider.");
                }).UseServiceProviderFactory(new DynamicProxyServiceProviderFactory())
                .RunConsoleAsync();
        }

        private static JObject CreateRegister(IServiceCollection services, IConfigurationSection section, IConfiguration configuration)
        {
            var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            if (environmentName == "Development")
            {
                string config = File.ReadAllText(Path.Combine(Directory.GetCurrentDirectory(), "appsettings.Development.json"));

                //return configuration.Get<JobAppSettingsDto>();
                return JObject.Parse(config);
            }
            else
            {
                var serviceProvider = services.BuildServiceProvider();
                var configClient = serviceProvider.GetService<INacosConfigClient>();

                var xx = section["DataId"];
                var yy = section["GroupId"];

                var res = configClient.GetConfigAsync(new GetConfigRequest
                {
                    DataId = section["DataId"],
                    Group = section["GroupId"],
                }).Result;

                if (!string.IsNullOrEmpty(res))
                {
                    //var appSettings = Newtonsoft.Json.JsonConvert.DeserializeObject<JobAppSettingsDto>(res);
                    var appSettings = JObject.Parse(res);
                    return appSettings;
                }

                throw new Exception("Not Found Nacos!");
            }
        }

        /// <summary>
        /// 新增AppSetting服务注册
        /// </summary>
        /// <param name="services">services</param>
        /// <param name="appSettingsDto">eventAppSettings实体</param>
        private static void AddAppSettingsServices(this IServiceCollection services, JObject appSettingsDto)
        {
            //services.AddOptions();
            //var config = new ConfigurationBuilder()
            //    .AddJsonStream(new MemoryStream(Encoding.ASCII.GetBytes(JsonConvert.SerializeObject(appSettingsDto))))
            //    .Build();
            //services.Configure<JobAppSettingsDto>(obj => config.Bind(obj));

            //var serviceProvider = services.BuildServiceProvider();
            var cache = provider.GetService<IMemoryCache>();
            var entryOptions = new MemoryCacheEntryOptions().SetPriority(CacheItemPriority.NeverRemove);
            cache.Set("AppSettings", appSettingsDto, entryOptions);


        }
    }
}
