﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Application.SyncUsers.Dtos.SaiWu
{
    public class OrganizationResultDto
    {
        public string Name { get; set; }
        public string NameUS { get; set; }
        public string NameTW { get; set; }
        public string ShortName { get; set; }

        /// <summary>
        /// 不用这个字段，用oId
        /// </summary>
        public string Code { get; set; }

        public string OID { get; set; }
        public string Level { get; set; }
        public string LevelName { get; set; }
        public string Status { get; set; }
        public string EstablishDate { get; set; }
        public string StartDate { get; set; }
        public string StopDate { get; set; }
        public string ChangeDate { get; set; }
        public string POIdOrgAdmin { get; set; }
        public string POIdOrgReserve2 { get; set; }
        public string POIdOrgReserve3 { get; set; }
        public string IsCurrentRecord { get; set; }
        public string HRBP { get; set; }
        public string ShopOwner { get; set; }
        public string AdministrativeAssistant { get; set; }
        public string PersonInChargeDeputy { get; set; }
        public string BusinessModifiedBy { get; set; }
        public string BusinessModifiedTime { get; set; }
        public string LegalMan { get; set; }
        public string Address { get; set; }
        public string Fax { get; set; }
        public string Postcode { get; set; }
        public string Phone { get; set; }
        public string Url { get; set; }
        public string Place { get; set; }
        public string Industry { get; set; }
        public string Description { get; set; }
        public string Number { get; set; }
        public string BroadType { get; set; }
        public string OrderAdmin { get; set; }
        public string OrderReserve2 { get; set; }
        public string OrderReserve3 { get; set; }
        public string Comment { get; set; }
        public string POIdOrgAdminTreePath { get; set; }
        public string POIdOrgAdminTreeLevel { get; set; }
        public string POIdOrgReserve2TreePath { get; set; }
        public string POIdOrgReserve2TreeLevel { get; set; }
        public string OIDOrganizationType { get; set; }
        public string EconomicType { get; set; }
        public string PersonInCharge { get; set; }
        public string FirstLevelOrganization { get; set; }
        public string SecondLevelOrganization { get; set; }
        public string ThirdLevelOrganization { get; set; }
        public string FourthLevelOrganization { get; set; }
        public string FifthLevelOrganization { get; set; }
        public string SixthLevelOrganization { get; set; }
        public string SeventhLevelOrganization { get; set; }
        public string EighthLevelOrganization { get; set; }
        public string NinthLevelOrganization { get; set; }
        public string TenthLevelOrganization { get; set; }
        public string OrderCode { get; set; }
        public string POIdOrgAdminNameTreePath { get; set; }
        public string IsVirtualOrg { get; set; }
        public string LeaderWithSpecificDuty { get; set; }
        public string IsMDMData { get; set; }
        public string ObjectId { get; set; }
        public string CreatedBy { get; set; }
        public string CreatedTime { get; set; }
        public string ModifiedBy { get; set; }
        public string ModifiedTime { get; set; }
        public bool StdIsDeleted { get; set; }
    }
}