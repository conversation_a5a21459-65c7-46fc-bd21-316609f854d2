﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Application.SyncUsers.Dtos.SaiWu
{
    public class EmployeeResultDto
    {
        public string UserID { get; set; } //": "179033980",
        public string Name { get; set; } //": "徐迎娣",
        public string PYName { get; set; } //": null,
        public string PhoneticOfXing { get; set; } //": null,
        public string PhoneticOfMing { get; set; } //": null,
        public string Gender { get; set; } //": "1",
        public string Email { get; set; } //": "<EMAIL>",
        public string IDType { get; set; } //": "1",
        public string IDTypeName { get; set; } //": "身份证",
        public string IDNumber { get; set; } //": "342221198708280546",
        public string IsLongTermCertificate { get; set; } //": false,
        public string CertificateStartDate { get; set; } //": "2014-01-13 00:00:00",
        public string CertificateValidityTerm { get; set; } //": "2034-01-13 00:00:00",
        public string Birthday { get; set; } //": "1987-08-28 00:00:00",
        public string WorkDate { get; set; } //": "2010-09-08 00:00:00",
        public string HomeAddress { get; set; } //": null,
        public string MobilePhone { get; set; } //": "18662260546",
        public string WeiXin { get; set; } //": null,
        public string IDPhoto { get; set; } //": "dfs://TenantBaseFile/110496/1686796858/3/51fc60004643431c9eb646b9a31c6b1e.jpg",
        public string SmallIDPhoto { get; set; } //": "dfs://TenantBaseFile/110496/1686794635/3/53d2c6fd8b1c43ccad138e72e4f19544.Jpeg",
        public string IDPortraitSide { get; set; } //": "dfs://TenantBaseFile/110496/1686794217/3/4bba0855b61d4a4cbaf7d64aa9e493df.jpg",
        public string IDCountryEmblemSide { get; set; } //": "dfs://TenantBaseFile/110496/1686794239/3/0fa6d5a0eb9945d0896e31ea82978382.jpg",
        public string AllowToLoginIn { get; set; } //": null,
        public string PersonalHomepage { get; set; } //": null,
        public string Speciality { get; set; } //": null,
        public string Major { get; set; } //": "人力资源管理",
        public string PostalCode { get; set; } //": null,
        public string PassportNumber { get; set; } //": null,
        public string Constellation { get; set; } //": null,
        public string BloodType { get; set; } //": null,
        public string ResidenceAddress { get; set; } //": null,
        public string JoinPartyDate { get; set; } //": null,
        public string DomicileType { get; set; } //": "2",
        public string EmergencyContact { get; set; } //": null,
        public string EmergencyContactRelationship { get; set; } //": null,
        public string EmergencyContactPhone { get; set; } //": "17751294586",
        public string QQ { get; set; } //": null,
        public string AboutMe { get; set; } //": null,
        public string HomePhone { get; set; } //": null,
        public string GraduateDate { get; set; } //": "2019-12-01 00:00:00",
        public string MarryCategory { get; set; } //": "1",
        public string MarryCategoryName { get; set; } //": "已婚",
        public string PoliticalStatus { get; set; } //": null,
        public string PoliticalStatusName { get; set; } //": null,
        public string Nationality { get; set; } //": "1",
        public string NationalityName { get; set; } //": "中国",
        public string Nation { get; set; } //": "1",
        public string NationName { get; set; } //": "汉族",
        public string Birthplace { get; set; } //": "江苏省阜宁县阜城镇城西居民四组12号",
        public string RegistAddress { get; set; } //": "3400",
        public string RegistAddressName { get; set; } //": "安徽省",
        public string EducationLevel { get; set; } //": "1",
        public string EducationLevelName { get; set; } //": "本科",
        public string LastSchool { get; set; } //": "南京大学",
        public string EngName { get; set; } //": null,
        public string Firstname { get; set; } //": null,
        public string Lastname { get; set; } //": null,
        public string OfficeTel { get; set; } //": null,
        public string BusinessAddress { get; set; } //": null,
        public string BackupMail { get; set; } //": "<EMAIL>",
        public string ApplicantId { get; set; } //": "69",
        public string ApplyIdV6 { get; set; } //": "c7b4686d-d407-4795-afb3-3b1f8f627ee8",
        public string ApplicantIdV6 { get; set; } //": "93088f11-c27d-4c58-9253-cfb76e0bd178",
        public string Age { get; set; } //": "35",
        public string BusinessModifiedBy { get; set; } //": "158001278",
        public string BusinessModifiedTime { get; set; } //": "2023-06-15 11:05:22",
        public string SourceType { get; set; } //": "0",
        public string SourceTypeName { get; set; } //": "员工",
        public string FirstEntryDate { get; set; } //": "2023-06-15 00:00:00",
        public string LatestEntryDate { get; set; } //": "2023-06-15 00:00:00",
        public string TutorNew { get; set; } //": null,
        public string IDFront { get; set; } //": null,
        public string IDBehind { get; set; } //": null,
        public string PreRetireDate { get; set; } //": null,
        public string ActualRetireDate { get; set; } //": null,
        public string IsConfirmRetireDate { get; set; } //": null,
        public string ActivationState { get; set; } //": "1",
        public string IssuingAuthority { get; set; } //": "阜宁县公安局",
        public string IsDisabled { get; set; } //": null,
        public string DisabledNumber { get; set; } //": null,
        public string EmployeeType { get; set; } //": "1",
        public string IsMDMData { get; set; } //": "0",
        public string ObjectId { get; set; } //": "f7d4515a-6bed-4e9c-aace-62c8cf3bcbb7",
        public string CreatedBy { get; set; } //": "177992986",
        public string CreatedTime { get; set; } //": "2023-06-14 17:23:30",
        public string ModifiedBy { get; set; } //": "10000",
        public string ModifiedTime { get; set; } //": "2023-06-15 11:05:22",
        public string StdIsDeleted { get; set; } //": false
    }
}
