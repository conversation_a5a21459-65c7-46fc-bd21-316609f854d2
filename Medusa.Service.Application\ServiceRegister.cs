using Medusa.Service.Application.SyncUsers;
using Medusa.Service.Core.ORM;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Medusa.Service.Application
{
    /// <summary>
    /// 服务注册
    /// </summary>
    public static class ServiceRegister
    {
        /// <summary>
        /// AddApplicationServices
        /// </summary>
        /// <param name="services">services</param>
        /// <param name="configuration">configuration</param>
        public static void AddPlatformServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton<MyDbContext>();
            services.AddSingleton<ISyncUserService, ShuiWuSyncUserService>();
            services.AddSingleton<SyncUserContext>();
        }
    }
}
