﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Core.Dtos
{
    /// <summary>
    /// JobAppSettingsDto
    /// </summary>
    public class JobAppSettingsDto
    {
        /// <summary>
        /// 数据库配置项
        /// </summary>
        public PersistenceDto Persistence { get; set; }

        /// <summary>
        /// 日志配置项
        /// </summary>
        public LoggingDto Logging { get; set; }

        /// <summary>
        /// 日志
        /// </summary>
        public LogSettingsDto LogSettings { get; set; }

        ///// <summary>
        ///// 同步Token
        ///// </summary>
        //public SyncTokenDto SyncToken { get; set; }

        ///// <summary>
        ///// 同步接口Urls
        ///// </summary>
        //public SyncUrlsDto SyncUrls { get; set; }

        ///// <summary>
        ///// 普通角色id
        ///// </summary>
        //public Guid NormalRoleId { get; set; }

        /// <summary>
        /// 系统名称
        /// </summary>
        public string SystemName { get; set; }

        /// <summary>
        /// 各个系统的配置项
        /// </summary>
        public object SystemSettings { get; set; }
    }
}
