using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Application.SyncUsers
{
    /// <summary>
    /// 同步结果
    /// </summary>
    /// <typeparam name="T">T</typeparam>
    public class SyncResult<T>
    {
        /// <summary>
        /// 当前页数
        /// </summary>
        public int Current { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int Pages { get; set; }

        /// <summary>
        /// true 有数据
        /// </summary>
        public bool SearchCount { get; set; }

        /// <summary>
        /// 每页多少个数据
        /// </summary>
        public int Size { get; set; }

        /// <summary>
        /// 总数据
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 真实数据
        /// </summary>
        public T Records { get; set; }
    }
}
