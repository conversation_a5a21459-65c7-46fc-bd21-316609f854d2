# deploy_copy 和 deploy_run 是 runner 支持的脚本
# 务必采用架构组提供的 runner 才能使用这两个命令
# 这两个命令分别是将 runner 的文件复制到部署服务器、在部署服务器上运行指定命令

stages:
  - lint_and_build
  - test
  - deploy
  - release

before_script:
  - if [[ -z $PROJECT_NAME ]]; then echo 'Missing Secret Variable -> $PROJECT_NAME' && exit 10; fi
  - SOURCE_PUBLIC=https://api.nuget.org/v3/index.json
  - SOURCE_PRIVATE=http://*************:15840/nuget
  - HARBOR=mtbpm.movitech.cn:8858/shuiwu
  - FilePath=shuiwu-project

lint_and_build_task:
  stage: lint_and_build
  except:
    - develop
    - master
    - tags
    - shuiwu-release
  script:
    - sed -i -e 's/Warning/Error/g' csharp.ruleset
    - dotnet restore -s $SOURCE_PUBLIC -s $SOURCE_PRIVATE
    - dotnet build ./Medusa.Service.SyncUsers/Medusa.Service.SyncUsers.csproj --no-restore

test_task:
  stage: test
  except:
    - develop
    - master
    - tags
    - shuiwu-release
  script:
    - dotnet restore -s $SOURCE_PUBLIC -s $SOURCE_PRIVATE

dev_deploy_task:
  stage: deploy
  only:
    - shuiwu-release
  script:
    - dotnet restore -s $SOURCE_PUBLIC -s $SOURCE_PRIVATE
    - dotnet publish ./Medusa.Service.SyncUsers/Medusa.Service.SyncUsers.csproj --no-restore -o /tmp/publish/$PROJECT_NAME/tmp
    - tar -cvzf /tmp/$PROJECT_NAME.tgz -C /tmp/publish/$PROJECT_NAME tmp
    - deploy_copy /tmp/$PROJECT_NAME.tgz /var/www/$FilePath/$PROJECT_NAME
    - deploy_run "cd /var/www/$FilePath/$PROJECT_NAME && tar -xvzf $PROJECT_NAME.tgz"
    - deploy_run "cd /var/www/$FilePath/$PROJECT_NAME && rm $PROJECT_NAME.tgz"
    - deploy_run "rm -rf /var/www/$FilePath/$PROJECT_NAME/src"
    - deploy_run "mv /var/www/$FilePath/$PROJECT_NAME/tmp /var/www/$FilePath/$PROJECT_NAME/src"

release_task:
  stage: release
  only:
    - tags
  script:
    - deploy_run "cd /var/www/$FilePath/$PROJECT_NAME/src && docker build -t $PROJECT_NAME:$CI_COMMIT_TAG  ."
    - deploy_run "docker tag $PROJECT_NAME:$CI_COMMIT_TAG  $HARBOR/$PROJECT_NAME:$CI_COMMIT_TAG"
    - deploy_run "docker push $HARBOR/$PROJECT_NAME:$CI_COMMIT_TAG"
    - deploy_run "docker rmi $HARBOR/$PROJECT_NAME:$CI_COMMIT_TAG && docker rmi $PROJECT_NAME:$CI_COMMIT_TAG"


