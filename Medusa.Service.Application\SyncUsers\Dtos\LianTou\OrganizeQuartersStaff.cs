using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Medusa.Service.Application.SyncUsers
{
    /// <summary>
    /// 组织岗位人员关系对象
    /// </summary>
    public class OrganizeQuartersStaff
    {
        /// <summary>
        /// 主键
        /// </summary>
        [FromQuery(Name = "id")]
        [JsonProperty(PropertyName = "id")]
        public string Id { get; set; }

        /// <summary>
        /// 组织ID，不唯一
        /// </summary>
        [FromQuery(Name = "FORGID")]
        [JsonProperty(PropertyName = "FORGID")]
        public string FORGID { get; set; }

        /// <summary>
        /// 人员集团工号
        /// </summary>
        [FromQuery(Name = "FUSERNUMBER")]
        [JsonProperty(PropertyName = "FUSERNUMBER")]
        public string FUSERNUMBER { get; set; }

        /// <summary>
        /// 人员HRM工号
        /// </summary>
        [FromQuery(Name = "FUSERNAME")]
        [JsonProperty(PropertyName = "FUSERNAME")]
        public string FUSERNAME { get; set; }

        /// <summary>
        /// 岗位编码
        /// </summary>
        [FromQuery(Name = "FPOSITIONID")]
        [JsonProperty(PropertyName = "FPOSITIONID")]
        public string FPOSITIONID { get; set; }

        /// <summary>
        /// 岗位名称
        /// </summary>
        [FromQuery(Name = "FJOBTITLE")]
        [JsonProperty(PropertyName = "FJOBTITLE")]
        public string FJOBTITLE { get; set; }

        /// <summary>
        /// 离职日期
        /// </summary>
        [FromQuery(Name = "ENDDATE")]
        [JsonProperty(PropertyName = "ENDDATE")]
        public string ENDDATE { get; set; }

        /// <summary>
        /// 入司时间
        /// </summary>
        [FromQuery(Name = "JOINWORKDATE")]
        [JsonProperty(PropertyName = "JOINWORKDATE")]
        public string JOINWORKDATE { get; set; }

        /// <summary>
        /// 上级主管工号
        /// </summary>
        [FromQuery(Name = "FATHERID")]
        [JsonProperty(PropertyName = "FATHERID")]
        public string FATHERID { get; set; }


        /// <summary>
        /// 创建时间
        /// </summary>
        [FromQuery(Name = "FCREATETIME")]
        [JsonProperty(PropertyName = "FCREATETIME")]
        public DateTime FCREATETIME { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [FromQuery(Name = "FUPDATETIME")]
        [JsonProperty(PropertyName = "FUPDATETIME")]
        public DateTime FUPDATETIME { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        [FromQuery(Name = "FGENDER")]
        [JsonProperty(PropertyName = "FGENDER")]
        public string FGENDER { get; set; }


        /// <summary>
        /// 邮箱
        /// </summary>
        [FromQuery(Name = "FEMAIL")]
        [JsonProperty(PropertyName = "FEMAIL")]
        public string FEMAIL { get; set; }

        /// <summary>
        /// 状态0=注销1=正常2=禁用
        /// </summary>
        [FromQuery(Name = "FSTATUS")]
        [JsonProperty(PropertyName = "FSTATUS")]
        public int FSTATUS { get; set; }

        /// <summary>
        ///  是否锁定 1=是，0=否；默认0
        /// </summary>
        [FromQuery(Name = "ISLOCKED")]
        [JsonProperty(PropertyName = "ISLOCKED")]
        public int ISLOCKED { get; set; }

        /// <summary>
        /// FMOBILE
        /// </summary>
        [FromQuery(Name = "FMOBILE")]
        [JsonProperty(PropertyName = "FMOBILE")]
        public string FMOBILE { get; set; }
    }
}
