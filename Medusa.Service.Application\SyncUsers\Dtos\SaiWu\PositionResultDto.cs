﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Application.SyncUsers.Dtos.SaiWu
{
    /// <summary>
    /// 职位信息->对应BPM的Positions表
    /// </summary>
    public class PositionResultDto
    {
        public string Name { get; set; } //": "总经理",
        public string NameTW { get; set; } //": null,
        public string NameUS { get; set; } //": null,
        public string Code { get; set; } //": "GW1102",
        public string OId { get; set; } //": "812f3bcb-831d-43a2-b1b1-09562fc75daf",
        public string EstablishDate { get; set; } //": "2023-04-27 00:00:00",
        public string StartDate { get; set; } //": "2023-04-27 00:00:00",
        public string StopDate { get; set; } //": "2023-06-11 00:00:00",
        public string Status { get; set; } //": "0",
        public string StatusName { get; set; } //": "停用",
        public string OIdJobPost { get; set; } //": null,
        public string JobPostName { get; set; } //": null,
        public string OIdJobGrade { get; set; } //": "",
        public string OIdJobLevelType { get; set; } //": null,
        public string OIdProfessionalLine { get; set; } //": "",
        public string OIdJobSequence { get; set; } //": null,
        public string OIdOrganization { get; set; } //": "3307109",
        public string OrgName { get; set; } //": "总经办",
        public string OIdJobLevel { get; set; } //": null,
        public string LevelName { get; set; } //": null,
        public string HighestOIdJobLevel { get; set; } //": null,
        public string HighLevelName { get; set; } //": null,
        public string BusinessModifiedBy { get; set; } //": "158001278",
        public string BusinessModifiedTime { get; set; } //": "2023-06-12 09:44:22",
        public string Description { get; set; } //": "",
        public string DescriptionTW { get; set; } //": null,
        public string DescriptionUS { get; set; } //": null,
        public string Place { get; set; } //": "",
        public string PositionKey { get; set; } //": "",
        public string PositionSecret { get; set; } //": "",
        public string Order { get; set; } //": null,
        public string Score { get; set; } //": null,
        public string OrderAdmin { get; set; } //": null,
        public string OrderReserve2 { get; set; } //": null,
        public string POIdPositionAdmin { get; set; } //": "",
        public string POIdPositionReserve2 { get; set; } //": "",
        public string JobRequirements { get; set; } //": "",
        public string IsMDMData { get; set; } //": "0",
        public string ObjectId { get; set; } //": "812f3bcb-831d-43a2-b1b1-09562fc75daf",
        public string CreatedBy { get; set; } //": "132057131",
        public string CreatedTime { get; set; } //": "2023-04-27 16:34:06",
        public string ModifiedBy { get; set; } //": "158001278",
        public string ModifiedTime { get; set; } //": "2023-06-12 09:44:22",
        public string StdIsDeleted { get; set; } //": false        
    }
}
