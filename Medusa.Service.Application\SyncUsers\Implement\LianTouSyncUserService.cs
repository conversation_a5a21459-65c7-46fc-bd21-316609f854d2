using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using Medusa.Service.Application.SyncUsers;
using Medusa.Service.Core.Dtos;
using Medusa.Service.Core.Entities.Platform;
using Medusa.Service.Core.ORM;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using MT.Enterprise.Utils;
using MT.Enterprise.Utils.Extensions;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;

namespace Medusa.Service.Application.SyncUsers
{
    /// <summary>
    /// 同步组织人员服务
    /// </summary>
    public class LianTouSyncUserService : ISyncUserService
    {
        private MyDbContext _dbContext;
        private JobAppSettingsDto _jobAppSettingsDto;
        private string _token;
        private DateTime _lastUpdateTime;
        private DateTime _thisUpdateTime;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serviceProvider">serviceProvider</param>
        public LianTouSyncUserService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            _jobAppSettingsDto = serviceProvider.GetService<IOptions<JobAppSettingsDto>>().Value;
            //  _token = GetToken();
        }

        public string SystemName => "LianTou";

        /// <summary>
        /// 同步用户
        /// </summary>
        public void SyncOrgAndUsersAsync()
        {
            /*
            //_lastUpdateTime = GetLastSyncDateTime();
            // _thisUpdateTime = DateTime.Now;


            // step1，同步组织
            // SyncOrganizations();

            // step2,更新组织全路径
            //  UpdateFullPath(_lastUpdateTime);

            // step2、同步人员
            SyncUsers();

            // step3、同步关系和岗位关联关系
            SyncUserPosition();

            // step4，同步岗位状态名称
            // SyncPositions();
            // UpdateLastSyncDateTime(_thisUpdateTime);
            */
        }

        /*

        /// <summary>
        /// 同步组织
        /// </summary>
        private void SyncOrganizations()
        {
            Console.WriteLine($"{DateTime.Now}SyncOrganizations: *************START************");

            (int current, int pageSize) = (1, 200);
            var myParams = new SearchDto
            {
                Current = current,
                Size = pageSize,
                StartTime = _lastUpdateTime,
            };

            //HttpRequestHeaderDto header = new HttpRequestHeaderDto
            //{
            //    Headers = new Dictionary<string, string>
            //    {
            //        { "Authorization", _token },
            //        { "CallerModule", _jobAppSettingsDto.SyncToken.CallerModule },
            //    },
            //};

            bool hasNext = true;
            var orgtables = _dbContext.Boost.Queryable<OrganizationTable>().ToList();
            var bpmOrgCodes = orgtables.Select(q => q.OrganizationCode).ToList();
            Console.WriteLine("=======>获取orgtables");
            while (hasNext)
            {
                var data = _dbContext.Boost.Queryable<Sync_org>().ToPageList(myParams.Current, pageSize);
                if (data != null && data?.Count > 0)
                {
                    myParams.Current = ++myParams.Current;

                    var mdmOrgCodes = data.Select(q => q.Code).ToList();

                    List<OrganizationTable> needInsertOrgs = new List<OrganizationTable>();
                    List<OrganizationTable> needUpdateOrgs = new List<OrganizationTable>();
                    try
                    {
                        needInsertOrgs = data.Where(q => !bpmOrgCodes.Contains(q.Code) && q.Id != null).ToList()
                           .Select(x => new OrganizationTable
                           {
                               OrganizationId = Guid.NewGuid(),
                               Name = x.Name,
                               OrganizationCode = x.Code,
                               FullPathText = x.FullName,
                               F1 = x.Id,
                               F2 = x.Parentid,
                               F3 = x.FullCode,
                               Status = 1,
                               CreateDate = DateTime.Now,
                               ModifyDate = DateTime.Now
                           }).ToList();
                        needUpdateOrgs = orgtables.Where(q => mdmOrgCodes.Contains(q.OrganizationCode)).ToList();
                        needUpdateOrgs.ForEach(q =>
                        {
                            var x = data.Find(z => z.Code == q.OrganizationCode);
                            q.Name = x.Name;
                            q.F2 = x.Parentid;
                            q.ModifyDate = DateTime.Now;
                        });

                        orgtables.AddRange(needInsertOrgs);
                        bpmOrgCodes.AddRange(needInsertOrgs.Select(a => a.OrganizationCode).ToList());
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(data.Where(q => !bpmOrgCodes.Contains(q.Code) && q.Id != null).ToList());
                        Console.WriteLine(ex);
                    }

                    _dbContext.Boost.Storageable(needInsertOrgs).ExecuteCommand();
                    _dbContext.Boost.Updateable(needUpdateOrgs).ExecuteCommand();
                    continue;
                }


                hasNext = false;
            }

            Console.WriteLine("SyncOrganizations *************END***********");
            Console.WriteLine($"SyncOrganizations-END:{DateTime.Now}");
        }

        private void UpdateFullPath(DateTime dateTime)
        {
            Console.WriteLine("UpdateFullPath *************START***********");
            Console.WriteLine($"UpdateFullPath-Start:{DateTime.Now}");
            var tableInfos = _dbContext.Boost.Queryable<OrganizationTable>().ToList();
            var rootInfo = tableInfos.First(q => q.OrganizationCode == "1002");
            var needUpdatePathList = tableInfos.Where(x => x.ModifyDate > dateTime && x.F2 != null).ToList();
            needUpdatePathList.ForEach(q =>
            {
                var fullPath = GetFullPath(tableInfos, q.F2, new KeyLabelPairBase());
                q.FullPathCode = fullPath.Key + "_" + q.OrganizationId + ".100";
                q.UpperId = tableInfos.FirstOrDefault(a => a.F1 == q.F2)?.OrganizationId ?? null;
            });

            bool hasNext = true;
            int listCount = (needUpdatePathList.Count / 500) + 1;
            while (hasNext)
            {
                for (int i = 0; i < listCount; i++)
                {
                    var test = needUpdatePathList.Take(500).ToList();
                    needUpdatePathList = needUpdatePathList.Skip(500).ToList();
                    _dbContext.Boost.Updateable(test).ExecuteCommand();
                }

                hasNext = false;
            }

            Console.WriteLine("UpdateFullPath *************END***********");
            Console.WriteLine($"UpdateFullPath-END:{DateTime.Now}");
        }

        private KeyLabelPairBase GetFullPath(List<OrganizationTable> list, string orgCode, KeyLabelPairBase fullPath)
        {
            var result = list.FirstOrDefault(a => a.F1 == orgCode && a.UpperId != Guid.Empty);
            if (result != null)
            {
                if (string.IsNullOrEmpty(result.F2))
                {
                    fullPath.Key = $"{result.OrganizationId}.10" + fullPath.Key;
                    fullPath.Label = $"_{result.Name}" + fullPath.Label;
                    return fullPath;
                }
                else
                {
                    fullPath.Key = $"_{result.OrganizationId}.100" + fullPath.Key;
                    fullPath.Label = $"_{result.Name}" + fullPath.Label;
                    return GetFullPath(list, result.F2, fullPath);
                }
            }

            return fullPath;
        }

        /// <summary>
        /// 同步岗位
        /// </summary>
        private void SyncPositions()
        {
            Console.WriteLine("SyncPositions *************START***********");
            Console.WriteLine($"SyncPositions-START:{DateTime.Now}");
            (int current, int pageSize) = (0, 200);
            var myParams = new SearchDto
            {
                Current = current,
                Size = pageSize,
                StartTime = _lastUpdateTime,
            };

            HttpRequestHeaderDto header = new HttpRequestHeaderDto
            {
                Headers = new Dictionary<string, string>
                {
                    { "Authorization", _token },
                    { "CallerModule", _jobAppSettingsDto.SyncToken.CallerModule },
                },
            };

            bool hasNext = true;
            #region 原抓取方法

            var bpmPositionCodes = _dbContext.Boost.Queryable<Position>().Select(q => q.F2).ToList();
            var bpmOrgs = _dbContext.Boost.Queryable<OrganizationTable>().ToList();
            while (hasNext)
            {
                var data = _dbContext.Boost.Queryable<Sync_users>().ToPageList(myParams.Current, pageSize);

                if (data != null && data?.Count > 0)
                {
                    myParams.Current = ++myParams.Current;
                    var mdmCodes = data.Where(q => !string.IsNullOrEmpty(q.FPOSITIONID)).Select(q => q.FPOSITIONID).Distinct().ToList();

                    // 新增岗位，由于MDM的岗位主键为string，所以还需要初始化主键
                    // var needInserts = totalResults.Where(q => !bpmPositionCodes.Contains(q.QuartersCode)).ToList().MapTo<List<Position>>();
                    // needInserts.ForEach(q => q.PositionId = Guid.NewGuid());

                    // 更新岗位
                    var needUpdates = _dbContext.Boost.Queryable<Position>().Where(q => mdmCodes.Contains(q.PositionCode)).ToList();
                    needUpdates.ForEach(q =>
                    {
                        var x = data.Find(z => z.FPOSITIONID == q.PositionCode);
                        q.Name = string.IsNullOrEmpty(x.FJOBTITLE) ? "员工" : x.FJOBTITLE;
                        q.OrganizationId = bpmOrgs.FirstOrDefault(a => a.F1 == x.FORGID)?.OrganizationId;
                        q.IsActive = x?.FSTATUS == 1;
                    });

                    _dbContext.Boost.Updateable(needUpdates).ExecuteCommand();

                    continue;
                }

                hasNext = false;


                hasNext = false;
            }
            #endregion
            Console.WriteLine("SyncPositions *************END***********");
            Console.WriteLine($"SyncPositions-END:{DateTime.Now}");
        }

        /// <summary>
        /// 同步用户
        /// </summary>
        private void SyncUsers()
        {
            Console.WriteLine("SyncUsers *************START***********");
            Console.WriteLine($"SyncUsers-START:{DateTime.Now}");

            (int current, int pageSize) = (1, 200);
            var myParams = new SearchDto
            {
                Current = current,
                Size = pageSize,
                StartTime = _lastUpdateTime,
            };

            //HttpRequestHeaderDto header = new HttpRequestHeaderDto
            //{
            //    Headers = new Dictionary<string, string>
            //    {
            //        { "Authorization", _token },
            //        { "CallerModule", _jobAppSettingsDto.SyncToken.CallerModule },
            //    },
            //};

            bool hasNext = true;
            var bpmUsers = _dbContext.Boost.Queryable<User>().ToList();
            var bpmUids = bpmUsers.Select(a => a.UserLoginId).ToList();
            var roleUserRelations = _dbContext.Boost.Queryable<RoleUserRelation>().ToList();

            while (hasNext)
            {
                var data = _dbContext.Boost.Queryable<Sync_users>().ToPageList(myParams.Current, pageSize);

                if (data != null && data.Count > 0)
                {
                    myParams.Current = ++myParams.Current;

                    var mdmIds = data.Select(q => q.PINYIN).ToList();

                    // 新增人员，由于MDM的岗位主键为string，所以还需要初始化主键
                    var needInserts = data.Where(q => !bpmUids.Contains(q.PINYIN)).ToList()
                        .Select(x => new User
                        {
                            UserId = Guid.NewGuid(),
                            UserLoginId = x.PINYIN,
                            UserName = x.FUSERNAME,
                            Gender = x.FGENDER,
                            Email = x.FEMAIL,
                            MobilePhone = x.FMOBILE,
                            Status = x.FSTATUS,
                            CreateDate = string.IsNullOrEmpty(x.FCREATETIME) ? DateTime.Now : Convert.ToDateTime(x.FCREATETIME),
                            ModifyDate = string.IsNullOrEmpty(x.FUPDATETIME) ? DateTime.Now : Convert.ToDateTime(x.FUPDATETIME),
                            LockStatus = x.ISLOCKED,
                            WorkNumber = x.FUSERNUMBER
                        }).ToList();
                    bpmUids.AddRange(needInserts.Select(q => q.UserLoginId).ToList());
                    var ids = new List<Guid>();

                    // 更新人员
                    var needUpdates2 = bpmUsers.Where(q => mdmIds.Contains(q.UserLoginId)).ToList();
                    var needUpdates = new List<User>();
                    needUpdates2.ForEach(q =>
                    {
                        var x = data.Find(z => z.PINYIN == q.UserLoginId);
                        if (x == null)
                        {
                            return;
                        }

                        if ((x.FSTATUS == 1 ? 1 : 0) != q.Status || x.FGENDER != q.Gender || x.FUSERNAME != q.UserName
                        || x.FEMAIL != q.Email || (x.FMOBILE.Length > 11 ? x.FMOBILE.Substring(0, 11) : x.FMOBILE) != q.MobilePhone
                        || x.FUSERNUMBER != q.WorkNumber)
                        {
                            q.Status = x.FSTATUS == 1 ? 1 : 0;
                            q.Gender = x.FGENDER;
                            q.UserName = x.FUSERNAME;
                            q.Email = x.FEMAIL;
                            q.MobilePhone = x.FMOBILE.Length > 11 ? x.FMOBILE.Substring(0, 11) : x.FMOBILE;
                            q.ModifyDate = string.IsNullOrEmpty(x.FUPDATETIME) ? DateTime.Now : Convert.ToDateTime(x.FUPDATETIME);
                            q.WorkNumber = x.FUSERNUMBER;
                            needUpdates.Add(q);
                        }
                    });

                    _dbContext.Boost.Storageable(needInserts).ExecuteCommand();
                    _dbContext.Boost.Updateable(needUpdates).ExecuteCommand();
                    continue;
                }

                hasNext = false;
            }

            Console.WriteLine("SyncUsers *************END***********");
            Console.WriteLine($"SyncUsers-END:{DateTime.Now}");
        }

        /// <summary>
        /// 同步人员与组织的关联关系
        /// </summary>
        private void SyncUserPosition()
        {
            Console.WriteLine("SyncUserPosition *************START***********");
            Console.WriteLine($"SyncUserPosition-START:{DateTime.Now}");
            (int current, int pageSize) = (0, 200);
            var myParams = new SearchDto
            {
                Current = current,
                Size = pageSize,
                StartTime = _lastUpdateTime,
            };

            HttpRequestHeaderDto header = new HttpRequestHeaderDto
            {
                Headers = new Dictionary<string, string>
                {
                    { "Authorization", _token },
                    { "CallerModule", _jobAppSettingsDto.SyncToken.CallerModule },
                },
            };

            bool hasNext = true;
            var totalPositions = _dbContext.Boost.Queryable<Position>().ToList();
            var totalUsers = _dbContext.Boost.Queryable<User>().ToList();
            var totalUserPositionRelations = _dbContext.Boost.Queryable<UserPositionRelation>().ToList();
            var bpmOrgs = _dbContext.Boost.Queryable<OrganizationTable>().ToList();
            while (hasNext)
            {
                var data = _dbContext.Boost.Queryable<Sync_users>().ToPageList(myParams.Current, pageSize);


                if (data != null && data?.Count > 0)
                {
                    myParams.Current = ++myParams.Current;

                    var needSaveUserPosition = new List<UserPositionRelation>();

                    var positionsCodes = totalPositions.Select(a => a.PositionCode).Distinct().ToList();

                    // 新增岗位，由于MDM的岗位主键为string，所以还需要初始化主键
                    var needInserts = data.Where(q => !string.IsNullOrEmpty(q.FPOSITIONID) && !positionsCodes.Contains(q.FPOSITIONID))
                        .Select(x => new Position
                        {
                            PositionId = Guid.NewGuid(),
                            OrganizationId = bpmOrgs.FirstOrDefault(a => a.F1 == x.FORGID)?.OrganizationId,
                            PositionCode = x.FPOSITIONID,
                            Name = string.IsNullOrEmpty(x.FJOBTITLE) ? "员工" : x.FJOBTITLE,
                            IsActive = true,
                        }).ToList();

                    totalPositions.AddRange(needInserts);
                    _dbContext.Boost.Storageable(needInserts).ExecuteCommand();

                    data.ForEach(z =>
                    {
                        var user = totalUsers.FirstOrDefault(q => q.UserLoginId == z.PINYIN);
                        var position = totalPositions.FirstOrDefault(q => q.PositionCode == z.FPOSITIONID);

                        if (user != null && position != null)
                        {
                            var userPosition = totalUserPositionRelations.FirstOrDefault(q => q.UserId == user.UserId);

                            // 当人员的岗位做了调整，需要更新人员岗位
                            if (userPosition != null)
                            {
                                if (userPosition.IsActive != (z.FSTATUS == 1))
                                {
                                    userPosition.IsActive = z.FSTATUS == 1;
                                    userPosition.StartDate = DateTime.Now;
                                    userPosition.EndDate = DateTime.Now.AddYears(100);
                                    userPosition.PrimaryPosition = true;
                                    userPosition.PositionId = position.PositionId;
                                    needSaveUserPosition.Add(userPosition);
                                }
                            }
                            else
                            {
                                needSaveUserPosition.Add(new UserPositionRelation
                                {
                                    UserPositionRelationId = Guid.NewGuid(),
                                    IsActive = z.FSTATUS == 1,
                                    PositionId = position.PositionId,
                                    UserId = user.UserId,
                                    StartDate = DateTime.Now,
                                    EndDate = DateTime.Now.AddYears(100),
                                    PrimaryPosition = true
                                });
                            }
                            totalUserPositionRelations.AddRange(needSaveUserPosition);
                        }
                    });

                    _dbContext.Boost.Storageable(needSaveUserPosition).ExecuteCommand();

                    continue;
                }

                hasNext = false;


                hasNext = false;
            }

            Console.WriteLine("SyncUserPosition *************END***********");
            Console.WriteLine($"SyncUserPosition-END:{DateTime.Now}");
        }

        /// <summary>
        /// 获取最后一次同步时间
        /// </summary>
        /// <returns>最后一次同步时间</returns>
        private DateTime GetLastSyncDateTime()
        {
            var lastSyncParam = _dbContext.Boost.Queryable<Parameter>().First(q => q.Code == "LastSyncDate");
            return string.IsNullOrEmpty(lastSyncParam?.Value) ? DateTime.MinValue : DateTime.Parse(lastSyncParam?.Value);
        }

        private void UpdateLastSyncDateTime(DateTime dateTime)
        {
            var lastSyncParam = _dbContext.Boost.Queryable<Parameter>().First(q => q.Code == "LastSyncDate");
            if (lastSyncParam == null)
            {
                lastSyncParam = new Parameter
                {
                    ParameterId = Guid.NewGuid(),
                    Code = "LastSyncDate",
                    Name = "最后一次同步时间",
                };
            }

            lastSyncParam.Value = dateTime.ToString("yyyy-MM-dd HH:mm:ss");
            _dbContext.Boost.Storageable(lastSyncParam).ExecuteCommand();
        }

        /// <summary>
        /// 验证token
        /// </summary>
        /// <returns>token</returns>
        private string GetToken()
        {
            return Md5Hash(string.Format("callerModule={0}&appKey={1}&secretKey={2}", _jobAppSettingsDto.SyncToken.CallerModule, _jobAppSettingsDto.SyncToken.AppKey, _jobAppSettingsDto.SyncToken.SecretKey));
        }

        /// <summary>
        /// 32位MD5加密
        /// </summary>
        /// <param name="input">input</param>
        /// <returns>加密信息</returns>
        private string Md5Hash(string input)
        {
            MD5CryptoServiceProvider md5Hasher = new MD5CryptoServiceProvider();
            byte[] data = md5Hasher.ComputeHash(Encoding.Default.GetBytes(input));
            StringBuilder sBuilder = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sBuilder.Append(data[i].ToString("x2"));
            }

            return sBuilder.ToString();
        }

        /// <summary>
        /// Base64 解码
        /// </summary>
        /// <param name="encode">解码方式</param>
        /// <param name="source">要解码的字符串</param>
        /// <returns>返回解码后的字符串</returns>
        private string DecodeBase64(Encoding encode, string source)
        {
            var bytes = Convert.FromBase64String(source);
            string result;
            try
            {
                result = encode.GetString(bytes);
            }
            catch
            {
                result = source;
            }

            return result;
        }
        */
    }
}
