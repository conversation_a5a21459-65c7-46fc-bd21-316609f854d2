using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Core.Entities.Platform
{
    /// <summary>
    /// 组织
    /// </summary>
    [EntityTable("Organizations", "组织")]
    public class OrganizationTable
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid OrganizationId { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [EntityColumn(ColumnDescription = "名称", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string Name { get; set; }

        /// <summary>
        /// 名称2
        /// </summary>
        [EntityColumn(ColumnDescription = "名称2", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string Name2 { get; set; }

        /// <summary>
        /// 部门编码
        /// </summary>
        [EntityColumn(ColumnDescription = "部门编码", ColumnDataType = "nvarchar(50)", IsNullable = true)]
        public string OrganizationCode { get; set; }

        /// <summary>
        /// 上级Id
        /// </summary>
        [EntityColumn(ColumnDescription = "上级Id", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public Guid? UpperId { get; set; }

        /// <summary>
        /// 负责人
        /// </summary>
        [EntityColumn(ColumnDescription = "负责人", ColumnDataType = "varchar(500)", IsNullable = true)]
        public string Manager { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "电话", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string Telephone { get; set; }

        /// <summary>
        /// 层级
        /// </summary>
        [EntityColumn(ColumnDescription = "层级", IsNullable = true)]
        public int? Level { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [EntityColumn(ColumnDescription = "状态", IsNullable = true)]
        public int? Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [EntityColumn(ColumnDescription = "备注", ColumnDataType = "ntext,text", IsNullable = true)]
        public string Remark { get; set; }

        /// <summary>
        /// SortCode
        /// </summary>
        [EntityColumn(ColumnDescription = "SortCode", IsNullable = true)]
        public int? SortCode { get; set; }

        /// <summary>
        /// 全路径
        /// </summary>
        [EntityColumn(ColumnDescription = "全路径", ColumnDataType = "nvarchar(500)", IsNullable = true)]
        public string FullPathText { get; set; }

        /// <summary>
        /// 全路径Code
        /// </summary>
        [EntityColumn(ColumnDescription = "全路径Code", ColumnDataType = "varchar(1000)", IsNullable = true)]
        public string FullPathCode { get; set; }

        /// <summary>
        /// 部门负责人
        /// </summary>
        [EntityColumn(ColumnDescription = "F1", ColumnDataType = "nvarchar(1000)", IsNullable = true)]
        public string F1 { get; set; }

        /// <summary>
        /// 父部门
        /// </summary>
        [EntityColumn(ColumnDescription = "F2", ColumnDataType = "nvarchar(1000)", IsNullable = true)]
        public string F2 { get; set; }

        /// <summary>
        /// F3 全路径code
        /// </summary>
        [EntityColumn(ColumnDescription = "F3", ColumnDataType = "nvarchar(1000)", IsNullable = true)]
        public string F3 { get; set; }

        /// <summary>
        /// 数据源类型
        /// </summary>
        [EntityColumn(ColumnDescription = "数据源类型", ColumnDataType = "varchar(10)", IsNullable = true)]
        public string SourceType { get; set; }

        /// <summary>
        /// 数据源
        /// </summary>
        [EntityColumn(ColumnDescription = "数据源", ColumnDataType = "nvarchar(50)", IsNullable = true)]
        public string Source { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [EntityColumn(ColumnDescription = "创建时间", ColumnDataType = "datetime", IsNullable = true)]
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [EntityColumn(ColumnDescription = "更新时间", ColumnDataType = "datetime", IsNullable = true)]
        public DateTime ModifyDate { get; set; }
    }
}
