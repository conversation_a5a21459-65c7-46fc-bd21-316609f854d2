using System;

namespace Medusa.Service.Application.SyncUsers.Dtos.ShuiWu
{
    /// <summary>
    /// 岗位
    /// </summary>
    public class PositionResultDto
    {
        /// <summary>
        /// 岗位编码
        /// </summary>
        public string postCode { get; set; }
        /// <summary>
        /// 岗位名称
        /// </summary>
        public string postName { get; set; }
        /// <summary>
        /// 岗位分类（高管、中层、基层）
        /// </summary>
        public string postType { get; set; }
        /// <summary>
        /// 岗位排序（升序）
        /// </summary>
        public int postSort { get; set; }
        /// <summary>
        /// 状态（0正常 1删除 2停用）
        /// </summary>
        public string status { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime createDate { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime updateDate { get; set; }
    }
}