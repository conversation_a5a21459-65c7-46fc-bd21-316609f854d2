﻿using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Core.Entities.Platform
{
    /// <summary>
    /// 变量
    /// </summary>
    [EntityTable("configsettings", "变量表")]
    public class ConfigSetting
    {
        /// <summary>
        /// Key
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "Key", ColumnDataType = "varchar(50)", IsNullable = false)]

        public string Key { get; set; }

        /// <summary>
        /// Value
        /// </summary>
        [EntityColumn(ColumnDescription = "Value", ColumnDataType = "varchar(210)", IsNullable = true)]
        public string Value { get; set; }

        /// <summary>
        /// Group
        /// </summary>
        [EntityColumn(ColumnDescription = "Group", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string Group { get; set; }

        /// <summary>
        /// Creator
        /// </summary>
        [EntityColumn(ColumnDescription = "Creator", ColumnDataType = "varchar(36)", IsNullable = true)]
        public string Creator { get; set; }

        /// <summary>
        /// CreatorName
        /// </summary>
        [EntityColumn(ColumnDescription = "CreatorName", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string CreatorName { get; set; }

        /// <summary>
        /// CreatorTime
        /// </summary>
        [EntityColumn(ColumnDescription = "CreatorTime", ColumnDataType = "datetime", IsNullable = true)]
        public DateTime? CreatorTime { get; set; }
    }
}
