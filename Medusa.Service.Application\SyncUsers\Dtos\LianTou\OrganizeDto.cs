using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Medusa.Service.Application.SyncUsers
{
    /// <summary>
    /// 组织对象
    /// </summary>
    public class OrganizeDto
    {
        /// <summary>
        /// 主键
        /// </summary>
        [FromQuery(Name = "id")]
        [JsonProperty(PropertyName = "id")]
        public string Id { get; set; }

        /// <summary>
        /// 组织ID，不唯一
        /// </summary>
        [FromQuery(Name = "Code")]
        [JsonProperty(PropertyName = "Code")]
        public string Code { get; set; }

        /// <summary>
        /// FullPathCode
        /// </summary>
        [FromQuery(Name = "FullCode")]
        [JsonProperty(PropertyName = "FullCode")]
        public string FullCode { get; set; }

        /// <summary>
        /// 组织名称
        /// </summary>
        [FromQuery(Name = "Name")]
        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }

        /// <summary>
        /// FullName
        /// </summary>
        [FromQuery(Name = "FullName")]
        [JsonProperty(PropertyName = "FullName")]
        public string FullName { get; set; }

        /// <summary>
        /// 父组织ID
        /// </summary>
        [FromQuery(Name = "parentId")]
        [JsonProperty(PropertyName = "parentId")]
        public string ParentId { get; set; }

        /// <summary>
        /// Level
        /// </summary>
        [FromQuery(Name = "level")]
        [JsonProperty(PropertyName = "level")]
        public string Level { get; set; }
    }
}
