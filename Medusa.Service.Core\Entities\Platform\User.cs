using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Core.Entities.Platform
{
    /// <summary>
    /// 人员
    /// </summary>
    [EntityTable("Users", "用户")]
    public class User
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid UserId { get; set; }

        /// <summary>
        /// 登录账号
        /// </summary>
        [EntityColumn(ColumnDescription = "登录账号", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string UserLoginId { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        [EntityColumn(ColumnDescription = "用户名称", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string UserName { get; set; }

        /// <summary>
        /// 用户别名
        /// </summary>
        [EntityColumn(ColumnDescription = "用户别名", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string UserAlias { get; set; }

        /// <summary>
        /// 登录密码
        /// </summary>
        [EntityColumn(ColumnDescription = "登录密码", ColumnDataType = "varchar(100)", IsNullable = true)]
        public string Password { get; set; }

        /// <summary>
        /// 用户类型
        /// </summary>
        [EntityColumn(ColumnDescription = "用户名称", ColumnDataType = "char(1)", IsNullable = true)]
        public string UserType { get; set; }

        /// <summary>
        /// 数据源类型
        /// </summary>
        [EntityColumn(ColumnDescription = "数据源类型", ColumnDataType = "char(1)", IsNullable = true)]
        public string SourceType { get; set; }

        /// <summary>
        /// 数据源
        /// </summary>
        [EntityColumn(ColumnDescription = "数据源", ColumnDataType = "nvarchar(20)", IsNullable = true)]
        public string Source { get; set; }

        /// <summary>
        /// First名称
        /// </summary>
        [EntityColumn(ColumnDescription = "First名称", ColumnDataType = "varchar(100)", IsNullable = true)]
        public string FirstName { get; set; }

        /// <summary>
        /// Middle名称
        /// </summary>
        [EntityColumn(ColumnDescription = "Middle名称", ColumnDataType = "varchar(100)", IsNullable = true)]
        public string MiddleName { get; set; }

        /// <summary>
        /// Last名称
        /// </summary>
        [EntityColumn(ColumnDescription = "Last名称", ColumnDataType = "varchar(100)", IsNullable = true)]
        public string LastName { get; set; }

        /// <summary>
        /// 名称拼音首字母
        /// </summary>
        [EntityColumn(ColumnDescription = "名称拼音首字母", ColumnDataType = "varchar(100)", IsNullable = true)]
        public string PinyinFirstWord { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        [EntityColumn(ColumnDescription = "性别", ColumnDataType = "char(1)", IsNullable = true)]
        public string Gender { get; set; }

        /// <summary>
        /// 邮件
        /// </summary>
        [EntityColumn(ColumnDescription = "邮件", ColumnDataType = "varchar(300)", IsNullable = true)]
        public string Email { get; set; }

        /// <summary>
        /// 后备邮件
        /// </summary>
        [EntityColumn(ColumnDescription = "后备邮件", ColumnDataType = "varchar(300)", IsNullable = true)]
        public string Emailbake { get; set; }

        /// <summary>
        /// 生日
        /// </summary>
        [EntityColumn(ColumnDescription = "生日", ColumnDataType = "datetime(3),datetime", IsNullable = true)]
        public DateTime? BirthDay { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [EntityColumn(ColumnDescription = "状态", IsNullable = true)]
        public int? Status { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "电话", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string MobilePhone { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [EntityColumn(ColumnDescription = "备注", ColumnDataType = "nvarchar(500)", IsNullable = true)]
        public string Remark { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        [EntityColumn(ColumnDescription = "创建人Id", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public Guid? CreateUserId { get; set; }

        /// <summary>
        /// 同步时间
        /// </summary>
        [EntityColumn(ColumnDescription = "创建时间", ColumnDataType = "datetime(3),datetime", IsNullable = true)]
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 修改人Id
        /// </summary>
        [EntityColumn(ColumnDescription = "修改人Id", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public Guid? MidifyUserId { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [EntityColumn(ColumnDescription = "修改时间", ColumnDataType = "datetime(3),datetime", IsNullable = true)]
        public DateTime? ModifyDate { get; set; }

        /// <summary>
        /// 最新登录时间
        /// </summary>
        [EntityColumn(ColumnDescription = "最新登录时间", ColumnDataType = "datetime(3),datetime", IsNullable = true)]
        public DateTime? LastestLoginDate { get; set; }

        /// <summary>
        /// 密码锁定时间
        /// </summary>
        [EntityColumn(ColumnDescription = "密码锁定时间", ColumnDataType = "datetime(3),datetime", IsNullable = true)]
        public DateTime? PasswordLockTime { get; set; }

        /// <summary>
        /// 锁定状态
        /// </summary>
        [EntityColumn(ColumnDescription = "锁定状态", IsNullable = true)]
        public int? LockStatus { get; set; }

        /// <summary>
        /// 工号
        /// </summary>
        [EntityColumn(ColumnDescription = "工号", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string WorkNumber { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [EntityColumn(ColumnDescription = "排序", IsNullable = true)]
        public int? SortCode { get; set; }

        /// <summary>
        /// 直属上级Id
        /// </summary>
        [EntityColumn(ColumnDescription = "直属上级Id", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public Guid? UpperUserId { get; set; }

        /// <summary>
        /// 主岗所属组织Id
        /// </summary>
        [EntityColumn(ColumnDescription = "主岗所属组织Id", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public Guid? OrganizationId { get; set; }

        /// <summary>
        /// 主岗所属组织名称
        /// </summary>
        [EntityColumn(ColumnDescription = "主岗所属组织名称", ColumnDataType = "nvarchar(100)", IsNullable = true)]
        public string OrganizationName { get; set; }

        /// <summary>
        /// 主岗所属组织全路径
        /// </summary>
        [EntityColumn(ColumnDescription = "主岗所属组织全路径", ColumnDataType = "varchar(4000)", IsNullable = true)]
        public string FullPathCode { get; set; }

        /// <summary>
        /// 主岗所属组织名称全路径
        /// </summary>
        [EntityColumn(ColumnDescription = "主岗所属组织全路径", ColumnDataType = "nvarchar(4000)", IsNullable = true)]
        public string FullPathText { get; set; }

        /// <summary>
        /// MDM的人员ID，非我们的工号，需要用MDM的人员ID反推我们的工号
        /// </summary>
        [EntityColumn(ColumnDescription = "F1", ColumnDataType = "nvarchar(1000)", IsNullable = true)]
        public string F1 { get; set; }

        /// <summary>
        /// F2
        /// </summary>
        [EntityColumn(ColumnDescription = "F2", ColumnDataType = "nvarchar(1000)", IsNullable = true)]
        public string F2 { get; set; }

        /// <summary>
        /// F3
        /// </summary>
        [EntityColumn(ColumnDescription = "F3", ColumnDataType = "nvarchar(1000)", IsNullable = true)]
        public string F3 { get; set; }

        /// <summary>
        /// F4
        /// </summary>
        [EntityColumn(ColumnDescription = "F4", ColumnDataType = "nvarchar(1000)", IsNullable = true)]
        public string F4 { get; set; }

        /// <summary>
        /// F5
        /// </summary>
        [EntityColumn(ColumnDescription = "F5", ColumnDataType = "nvarchar(1000)", IsNullable = true)]
        public string F5 { get; set; }

        /// <summary>
        /// F6
        /// </summary>
        [EntityColumn(ColumnDescription = "F6", ColumnDataType = "nvarchar(1000)", IsNullable = true)]
        public string F6 { get; set; }
    }
}
