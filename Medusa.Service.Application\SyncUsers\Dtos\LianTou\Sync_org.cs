﻿using MT.Enterprise.Core.ORM;
using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Application.SyncUsers
{
    /// <summary>
    /// 系统角色
    /// </summary>
    [EntityTable("Sync_orgs")]
    public class Sync_org
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public string Id { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [EntityColumn(ColumnDescription = "Code", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string Code { get; set; }

        /// <summary>
        /// 名称2
        /// </summary>
        [EntityColumn(ColumnDescription = "FullCode", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string FullCode { get; set; }

        /// <summary>
        /// Name
        /// </summary>
        [EntityColumn(ColumnDescription = "Name", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string Name { get; set; }

        /// <summary>
        /// FullName
        /// </summary>
        [EntityColumn(ColumnDescription = "FullName", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public string FullName { get; set; }

        /// <summary>
        /// Parentid
        /// </summary>
        [EntityColumn(ColumnDescription = "父Id", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public string Parentid { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "等级", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string Level { get; set; }

    }
}
