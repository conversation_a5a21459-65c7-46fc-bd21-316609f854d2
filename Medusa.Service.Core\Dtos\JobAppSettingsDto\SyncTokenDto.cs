using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Core.Dtos
{
    /// <summary>
    /// SyncTokenDto
    /// </summary>
    public class SyncTokenDto
    {
        /// <summary>
        /// CallerModule
        /// </summary>
        public string CallerModule { get; set; }

        /// <summary>
        /// AppKey
        /// </summary>
        public string AppKey { get; set; }

        /// <summary>
        /// SecretKey
        /// </summary>
        public string <PERSON><PERSON><PERSON> { get; set; }
    }
}
