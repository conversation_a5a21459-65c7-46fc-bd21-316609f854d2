using System;

namespace Medusa.Service.Application.SyncUsers.Dtos.ShuiWu
{
    /// <summary>
    /// 组织
    /// </summary>
    public class OrganizationResultDto
    {
        /// <summary>
        /// 部门编码
        /// </summary>
        public string officeCode { get; set; }
        /// <summary>
        /// 部门名称
        /// </summary>
        public string officeName { get; set; }
        /// <summary>
        /// 父级编码
        /// </summary>
        public string parentCode { get; set; }
        /// <summary>
        /// 父级名称
        /// </summary>
        public string parentName { get; set; }
        /// <summary>
        /// 父级编码全链路
        /// </summary>
        public string parentCodes { get; set; }
        /// <summary>
        /// 状态（0正常 1删除 2停用）
        /// </summary>
        public string status { get; set; }
        /// <summary>
        /// 排序码
        /// </summary>
        public int treeSort { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime createDate { get; set; }
        /// <summary>
        /// 最后一次修改时间
        /// </summary>
        public DateTime updateDate { get; set; }
        
        /// <summary>
        /// 领导
        /// </summary>
        public string leaderCodes { get; set; }
        
        /// <summary>
        /// 分管领导
        /// </summary>
        public string branchLeaderCodes { get; set; }
        
        /// <summary>
        /// 人资部门id
        /// </summary>
        public string shuiwuDeptId { get; set; }
    }
}