using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Core.Entities.Platform
{
    /// <summary>
    /// 岗位
    /// </summary>
    [EntityTable("Positions", "岗位")]
    public class Position
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? PositionId { get; set; }

        /// <summary>
        /// 组织Id
        /// </summary>
        [EntityColumn(ColumnDescription = "组织Id", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public Guid? OrganizationId { get; set; }

        /// <summary>
        /// 岗位编码不唯一
        /// </summary>
        [EntityColumn(ColumnDescription = "岗位编码", ColumnDataType = "nvarchar(100)", IsNullable = true)]
        public string PositionCode { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [EntityColumn(ColumnDescription = "名称", ColumnDataType = "varchar(100)", IsNullable = true)]
        public string Name { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [EntityColumn(ColumnDescription = "描述", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string Description { get; set; }

        /// <summary>
        /// 父级Id
        /// </summary>
        [EntityColumn(ColumnDescription = "父级Id", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public Guid? UpperId { get; set; }

        /// <summary>
        /// 是否有效
        /// </summary>
        [EntityColumn(ColumnDescription = "是否有效", IsNullable = true)]
        public bool? IsActive { get; set; }

        /// <summary>
        /// 岗位类型
        /// </summary>
        [EntityColumn(ColumnDescription = "岗位类型", IsNullable = true)]
        public int? Type { get; set; }

        /// <summary>
        /// 招商接口返回Id
        /// </summary>
        [EntityColumn(ColumnDescription = "F1", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string F1 { get; set; }

        /// <summary>
        /// 接口返回quartersCode
        /// </summary>
        [EntityColumn(ColumnDescription = "F2", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string F2 { get; set; }

        /// <summary>
        /// F3
        /// </summary>
        [EntityColumn(ColumnDescription = "F3", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string F3 { get; set; }

        /// <summary>
        /// F4
        /// </summary>
        [EntityColumn(ColumnDescription = "F4", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string F4 { get; set; }

        /// <summary>
        /// F5
        /// </summary>
        [EntityColumn(ColumnDescription = "F5", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string F5 { get; set; }
    }
}
