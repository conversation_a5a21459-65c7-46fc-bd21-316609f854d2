using System;
using System.Collections.Generic;

namespace Medusa.Service.Application.SyncUsers.Dtos.ShuiWu
{
    /// <summary>
    /// 用户
    /// </summary>
    public class EmployeeResultDto
    {
        public EmployeeResultDto()
        {
            deputyPostList = new List<EmployeeDeputyPostResultDto>();
        }
        
        /// <summary>
        /// 用户登录名
        /// </summary>
        public string loginCode { get; set; }
        /// <summary>
        /// 中文姓名
        /// </summary>
        public string userName { get; set; }
        /// <summary>
        /// 用户编码
        /// </summary>
        public string userCode { get; set; }
        /// <summary>
        /// 办公电话
        /// </summary>
        public string phone { get; set; }
        /// <summary>
        /// 邮箱
        /// </summary>
        public string email { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public string sex { get; set; }
        /// <summary>
        /// 用户权重，越小排序约靠前
        /// </summary>
        public int? userWeight { get; set; }
        /// <summary>
        /// 手机毫秒
        /// </summary>
        public string mobile { get; set; }
        /// <summary>
        /// 入职日期
        /// </summary>
        public string entryDate { get; set; }
        /// <summary>
        /// 出生日期
        /// </summary>
        public string birthday { get; set; }
        /// <summary>
        /// 部门编码
        /// </summary>
        public string officeCode { get; set; }
        /// <summary>
        /// 岗位编码（主）
        /// </summary>
        public string postCode { get; set; }
        /// <summary>
        /// 岗位名称
        /// </summary>
        public string postName { get; set; }
        /// <summary>
        /// 状态（0正常 1删除 2停用 3冻结）
        /// </summary>
        public string status { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime createDate { get; set; }
        /// <summary>
        /// 最后一次修改时间
        /// </summary>
        public DateTime updateDate { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string levelId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string levelCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string levelName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<EmployeeDeputyPostResultDto > deputyPostList { get; set; }
        
        /// <summary>
        /// 冗余BPM的UserId
        /// </summary>
        public Guid? BpmUserId { get; set; }
        
        /// <summary>
        /// 人资用户Id
        /// </summary>
        public string shuiwuUserId { get; set; }
    }
}