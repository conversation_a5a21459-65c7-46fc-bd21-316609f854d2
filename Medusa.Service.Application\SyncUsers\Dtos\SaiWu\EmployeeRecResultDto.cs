﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Application.SyncUsers.Dtos.SaiWu
{
    public class EmployeeRecResultDto
    {
        public string UserID { get; set; } //"": "179033980",
        public string PObjectDataID { get; set; } //": "f7d4515a-6bed-4e9c-aace-62c8cf3bcbb7",
        public string OIdDepartment { get; set; } //": "3307146",
        public string StartDate { get; set; } //": "2023-06-15 00:00:00",
        public string StopDate { get; set; } //": "9999-12-31 00:00:00",
        public string JobNumber { get; set; } //": "17317",
        public string EntryDate { get; set; } //": "2023-06-15 00:00:00",
        public string LastWorkDate { get; set; } //": null,
        public string RegularizationDate { get; set; } //": null,
        public string Probation { get; set; } //": "6",
        public string Order { get; set; } //": null,
        public string EmployType { get; set; } //": "0",
        public string ServiceType { get; set; } //": "0",
        public string ServiceStatus { get; set; } //": "0",
        public string ApprovalStatus { get; set; } //": "4",
        public string EmploymentSource { get; set; } //": null,
        public string EmploymentForm { get; set; } //": "ce697722-29ae-4e76-8c89-33d3214b8676",
        public string EmploymentFormName { get; set; } //": "合同用工",
        public string IsCharge { get; set; } //": "0",
        public string OIdJobPost { get; set; } //": "641566",
        public string OIdJobSequence { get; set; } //": "31036",
        public string OIdProfessionalLine { get; set; } //": null,
        public string OIdJobPosition { get; set; } //": "fa89e6ec-1e7b-4ca7-9fb4-c7c19ae98eed",
        public string OIdJobLevel { get; set; } //": "b1f5ae6c-6ee1-4eed-ba90-42abad797aea",
        public string OIdJobLevelName { get; set; } //": "三",
        public string OidJobGrade { get; set; } //": null,
        public string OidJobGradeName { get; set; } //": null,
        public string Place { get; set; } //": "320508",
        public string EmployeeStatus { get; set; } //": "2",
        public string EmploymentType { get; set; } //": "1084de0f-e582-421d-af16-1c52a0bef03f",
        public string EmploymentTypeName { get; set; } //": "间接",
        public string EmploymentChangeID { get; set; } //": "611ac72a-25c8-4247-b1ae-a6df56d67bdd",
        public string ChangedStatus { get; set; } //": null,
        public string POIdEmpAdmin { get; set; } //": "158000539",
        public string POIdEmpReserve2 { get; set; } //": null,
        public string BusinessTypeOID { get; set; } //": "1",
        public string ChangeTypeOID { get; set; } //": "1",
        public string EntryStatus { get; set; } //": "0",
        public string IsCurrentRecord { get; set; } //": true,
        public string LUOffer { get; set; } //": "0e0bbd6b-b571-44f9-a255-8a06de02dd4c",
        public string EntryType { get; set; } //": "1",
        public string WorkYearBefore { get; set; } //": null,
        public string WorkYearGroupBefore { get; set; } //": null,
        public string WorkYearCompanyBefore { get; set; } //": "0.00",
        public string WorkYearTotal { get; set; } //": "12.70",
        public string WorkYearGroupTotal { get; set; } //": null,
        public string WorkYearCompanyTotal { get; set; } //": "0.00",
        public string OIdOrganization { get; set; } //": "3307084",
        public string Whereabouts { get; set; } //": null,
        public string BlackStaffDesc { get; set; } //": null,
        public string BlackListAddReason { get; set; } //": null,
        public string TransitionTypeOID { get; set; } //": null,
        public string ChangeReason { get; set; } //": null,
        public string ProbationResult { get; set; } //": null,
        public string ProbationActualStopDate { get; set; } //": null,
        public string ProbationStartDate { get; set; } //": "2023-06-15 00:00:00",
        public string ProbationStopDate { get; set; } //": "2023-12-14 00:00:00",
        public string IsHaveProbation { get; set; } //": "1",
        public string Remarks { get; set; } //": null,
        public string AddOrNotBlackList { get; set; } //": null,
        public string BusinessModifiedBy { get; set; } //": "*********",
        public string BusinessModifiedTime { get; set; } //": "2023-06-15 11:05:22",
        public string TraineeStartDate { get; set; } //": null,
        public string IsMDMData { get; set; } //": "0",
        public string IsMainJob { get; set; } // "1" 主岗
        public string ObjectId { get; set; } //": "6ea41302-1401-41f2-82c6-b02889d40020",
        public string CreatedBy { get; set; } //": "177992986",
        public string CreatedTime { get; set; } //": "2023-06-14 17:23:30",
        public string ModifiedBy { get; set; } //": "*********",
        public string ModifiedTime { get; set; } //": "2023-06-15 11:05:22",
        public bool StdIsDeleted { get; set; } //": false
    }
}
