﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.DependencyInjection;

namespace Medusa.Service.Application.SyncUsers
{
    public class SyncUserContext
    {
        readonly IEnumerable<ISyncUserService> _syncUserServices;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serviceProvider">serviceProvider</param>
        public SyncUserContext(IServiceProvider serviceProvider)
        {
            _syncUserServices = serviceProvider.GetService<IEnumerable<ISyncUserService>>();
        }

        /// <summary>
        /// 匹配自动同步服务
        /// </summary>
        /// <param name="systemName">系统名称</param>
        /// <returns>实现</returns>
        public ISyncUserService GetImplementation(string systemName)
        {
            var x = _syncUserServices.FirstOrDefault(q => q.SystemName == systemName);
            return x;
        }
    }
}
