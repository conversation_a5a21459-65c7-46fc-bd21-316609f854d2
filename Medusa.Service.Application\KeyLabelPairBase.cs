using System;
using Newtonsoft.Json;

namespace Medusa.Service.Application
{
    /// <summary>
    /// Item基类
    /// </summary>
    [Serializable]
    public class KeyLabelPairBase
    {
        /// <summary>
        /// 名称
        /// </summary>
        /// <value>string</value>
        [JsonProperty(PropertyName = "label")]
        public string Label { get; set; }

        /// <summary>
        /// 值/主键
        /// </summary>
        /// <value>string </value>
        [JsonProperty(PropertyName = "key")]
        public string Key { get; set; }
    }
}
