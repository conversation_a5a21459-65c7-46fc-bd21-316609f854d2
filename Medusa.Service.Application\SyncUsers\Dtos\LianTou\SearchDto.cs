using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Medusa.Service.Application.SyncUsers
{
    /// <summary>
    /// 同步用户-查询参数
    /// </summary>
    public class SearchDto
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        [FromQuery(Name = "startTime")]
        [JsonProperty(PropertyName = "startTime")]
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [FromQuery(Name = "endTime")]
        [JsonProperty(PropertyName = "endTime")]
        public DateTime EndTime { get; set; } = DateTime.Now.AddYears(1);

        /// <summary>
        /// 页码
        /// </summary>
        [FromQuery(Name = "current")]
        [JsonProperty(PropertyName = "current")]
        public int Current { get; set; } = 0;

        /// <summary>
        /// 数据量
        /// </summary>
        [FromQuery(Name = "size")]
        [JsonProperty(PropertyName = "size")]
        public int Size { get; set; } = 10;

        /// <summary>
        /// 不填获取所有，Y有效，非Y无效
        /// </summary>
        [FromQuery(Name = "isValid")]
        [JsonProperty(PropertyName = "isValid")]
        public string IsValid { get; set; }
    }
}
