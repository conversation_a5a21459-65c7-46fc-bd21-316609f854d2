﻿using MT.Enterprise.Core.ORM;
using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Application.SyncUsers
{
    /// <summary>
    /// 系统角色
    /// </summary>
    [EntityTable("Sync_users")]
    public class Sync_users
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public string Fid { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [EntityColumn(ColumnDescription = "FUSERNUMBER", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string FUSERNUMBER { get; set; }

        /// <summary>
        /// 名称2
        /// </summary>
        [EntityColumn(ColumnDescription = "FUSERNAME", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string FUSERNAME { get; set; }

        /// <summary>
        /// Name
        /// </summary>
        [EntityColumn(ColumnDescription = "FREALNAME", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string FREALNAME { get; set; }

        /// <summary>
        /// FullName
        /// </summary>
        [EntityColumn(ColumnDescription = "FMOBILE", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public string FMOBILE { get; set; }

        /// <summary>
        /// Parentid
        /// </summary>
        [EntityColumn(ColumnDescription = "FORGID", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public string FORGID { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "FGENDER", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string FGENDER { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "FEMAIL", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string FEMAIL { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "FJOBTITLE", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string FJOBTITLE { get; set; }


        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "FBIRTHDAY", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string FBIRTHDAY { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "FORGUSERTYPE", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string FORGUSERTYPE { get; set; }


        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "FSTATUS", ColumnDataType = "varchar(50)", IsNullable = true)]
        public int FSTATUS { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "OPENID", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string OPENID { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "FCREATETIME", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string FCREATETIME { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "FUPDATETIME", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string FUPDATETIME { get; set; }


        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "FUSERTYPE", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string FUSERTYPE { get; set; }


        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "FPASSWORD2", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string FPASSWORD2 { get; set; }


        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "FQUESTION", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string FQUESTION { get; set; }


        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "FANSWER", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string FANSWER { get; set; }


        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "FISFIRSTPSNDOC", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string FISFIRSTPSNDOC { get; set; }


        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "FISSYNCYUN", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string FISSYNCYUN { get; set; }


        /// <summary>
        /// FPOSITIONID
        /// </summary>
        [EntityColumn(ColumnDescription = "FPOSITIONID", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string FPOSITIONID { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "IDCARD", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string IDCARD { get; set; }


        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "TEL", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string TEL { get; set; }


        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "ISHIDEPHONE", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string ISHIDEPHONE { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "ISOA", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string ISOA { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "ISFB", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string ISFB { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "ISLOCKED", ColumnDataType = "varchar(50)", IsNullable = true)]
        public int ISLOCKED { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "PINYIN", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string PINYIN { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "SYNCRANGE", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string SYNCRANGE { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [EntityColumn(ColumnDescription = "FATHERID", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string FATHERID { get; set; }

        /// <summary>
        /// USERID
        /// </summary>
        [EntityColumn(ColumnDescription = "USERID", ColumnDataType = "varchar(50)", IsNullable = true)]
        public string USERID { get; set; }
    }
}
