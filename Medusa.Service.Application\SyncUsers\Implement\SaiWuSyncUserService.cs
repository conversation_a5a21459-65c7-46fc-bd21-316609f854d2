﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Medusa.Service.Application.SyncUsers;
using Medusa.Service.Application.SyncUsers.Dtos.SaiWu;
using Medusa.Service.Core.Dtos;
using Medusa.Service.Core.Entities.Platform;
using Medusa.Service.Core.ORM;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using MT.Enterprise.Utils;
using MT.Enterprise.Utils.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.SS.Formula.Functions;
using SqlSugar;

namespace Medusa.Service.Application.SyncUsers
{
    /// <summary>
    /// 同步组织人员服务
    /// </summary>
    public class SaiWuSyncUserService : ISyncUserService
    {
        private MyDbContext _dbContext;
        private JObject _appSettingsDto;
        private DateTime _lastUpdateTime;
        private DateTime _thisUpdateTime;
        private AccessTokenDto _accessTokenDto;

        private List<OrganizationTable> _syncManageOrganizations = new List<OrganizationTable>(); // 专门处理部门负责人，防止部门同步时人员还没进来
        private List<User> _syncUserUpperIds = new List<User>(); // 专门处理人员所属上级

        private int _maxCount => 10000; // 防止程序出现死循环导致吃光内存

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serviceProvider">serviceProvider</param>
        public SaiWuSyncUserService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            _appSettingsDto = serviceProvider.GetService<IMemoryCache>().Get<JObject>("AppSettings");
        }

        public string SystemName => "SaiWu";

        /// <summary>
        /// 同步入口
        /// </summary>
        public void SyncOrgAndUsersAsync()
        {
            // 同步方案：http://confluence.movit-tech.com:1020/pages/viewpage.action?pageId=29886962

            try
            {
                _thisUpdateTime = DateTime.Now;
                GetLastSyncDateTime();

                Console.WriteLine(" 请求主数据Token开始！");
                GetAccessTokenAsync();
                if (_accessTokenDto == null)
                {
                    Console.WriteLine($"未获取到token，停止请求。");
                    return;
                }

                Console.WriteLine(" 同步组织开始！");
                SyncOrganizations();

                Console.WriteLine(" 同步岗位基本信息开始！");
                SyncPositions();

                Console.WriteLine(" 同步人员开始！");
                SyncUsers();

                Console.WriteLine(" 同步人员岗位开始！");
                SyncUserPositions();

                Console.WriteLine(" 更新部门经理开始！");
                UpdateOrganizationManager();

                Console.WriteLine(" 更新上级负责人开始！");
                UpdateUpperIds();

                Console.WriteLine(" 补充组织全路径开始！");
                UpdateFullPath();

                UpdateLastSyncDateTime(_thisUpdateTime);
                Console.WriteLine(" 同步结束！");
            }
            catch (Exception ex)
            {
                Console.Write($"错误提示：{ex.Message}");
            }
        }

        #region 记录上一次更新时间相关
        /// <summary>
        /// 获取最后一次同步时间
        /// </summary>
        private void GetLastSyncDateTime()
        {
            var lastSyncParam = _dbContext.Boost.Queryable<ConfigSetting>().First(q => q.Key == "LastSyncDate");
            _lastUpdateTime = string.IsNullOrEmpty(lastSyncParam?.Value) ? DateTime.MinValue : DateTime.Parse(lastSyncParam?.Value);
            Console.WriteLine($"上一次同步时间是：{_lastUpdateTime}");
        }

        private void UpdateLastSyncDateTime(DateTime date)
        {
            var lastSyncParam = _dbContext.Boost.Queryable<ConfigSetting>().First(q => q.Key == "LastSyncDate");
            if (lastSyncParam != null)
            {
                lastSyncParam.Value = date.ToOurString(Clock.ClockType.DateTime);
                lastSyncParam.Group = "SyncMDM";
                lastSyncParam.CreatorName = "自动同步";
                lastSyncParam.CreatorTime = DateTime.Now;
            }
            else
            {
                lastSyncParam = new ConfigSetting
                {
                    Key = "LastSyncDate",
                    Value = date.ToOurString(Clock.ClockType.DateTime),
                    Group = "SyncMDM",
                    CreatorName = "自动同步",
                    CreatorTime = DateTime.Now,
                };
            }

            _dbContext.Boost.Storageable(lastSyncParam).ExecuteCommand();
        }

        #endregion

        #region 主数据Token请求相关

        /// <summary>
        /// 获取Token
        /// </summary>
        private void GetAccessTokenAsync()
        {
            var result = string.Empty;
            var url = _appSettingsDto["SystemSettings"]?["SaiWu"]?["GetTokenUrl"].ToString();
            Dictionary<string, string> bodyContent = new Dictionary<string, string>();
            bodyContent.Add("client_id", _appSettingsDto["SystemSettings"]?["SaiWu"]?["TokenClientId"].ToString());
            bodyContent.Add("client_secret", _appSettingsDto["SystemSettings"]?["SaiWu"]?["TokenClientSecret"].ToString());
            bodyContent.Add("grant_type", _appSettingsDto["SystemSettings"]?["SaiWu"]?["TokenGrantType"].ToString());

            using (HttpClient _httpClient = new HttpClient())
            {
                HttpContent httpContent = new FormUrlEncodedContent(bodyContent);

                // 它的token获取方式不是通过json取的，无法使用Utils.HttpHelper方法
                httpContent.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");
                var response = _httpClient.PostAsync(url, httpContent).Result;
                result = response.Content.ReadAsStringAsync().Result;
            }

            _accessTokenDto = JsonConvert.DeserializeObject<AccessTokenDto>(result);
        }

        /// <summary>
        /// 拼接请求头
        /// </summary>
        /// <returns></returns>
        private Dictionary<string, string> CreateHeader()
        {
            GetAccessTokenAsync();

            if (_accessTokenDto == null)
            {
                throw new Exception("空令牌");
            }

            // Todo，如何检测这个令牌未超时？
            Dictionary<string, string> dic = new Dictionary<string, string>();
            dic.Add("Authorization", $"Bearer {_accessTokenDto.AccessToken}");
            return dic;
        }
        #endregion

        #region 同步组织相关
        /// <summary>
        /// 同步组织
        /// </summary>
        private void SyncOrganizations()
        {
            var url = _appSettingsDto["SystemSettings"]?["SaiWu"]?["GetOrganizationsUrl"].ToString();
            bool hasNext = true;
            int count = 0, size = 50, page = 1;
            while (hasNext)
            {
                count++;
                Thread.Sleep(50);
                if (count > _maxCount)
                {
                    hasNext = false;
                }

                var xxx = _lastUpdateTime.ToOurString(Clock.ClockType.DateTime);

                var body = new RequestDto
                {
                    Timestamp = _lastUpdateTime.ToOurString(Clock.ClockType.DateTime),
                    Size = size,
                    Page = page,
                };

                var response = HttpHelper.Post<ResponseDto<OrganizationResultDto>>(url, body, new HttpRequestHeaderDto { Headers = CreateHeader(), });
                if (response == null || response.ErrorCode != "0")
                {
                    throw new Exception($"读取组织失败:{response.ErrorMessage}");
                }
                else if (response.Data.Items?.Count > 0)
                {
                    Console.WriteLine($"正在准备同步前{count * size}笔，总条数{response.Data.Total}");

                    // step1、先把基本信息（不包括上级关系、全路径）同步进系统
                    List<OrganizationTable> organizations = new List<OrganizationTable>();
                    response.Data.Items.ForEach(q =>
                    {
                        var tempOrg = _dbContext.Boost.Queryable<OrganizationTable>().First(z => z.OrganizationCode == q.OID);
                        if (tempOrg == null)
                        {
                            tempOrg = new OrganizationTable
                            {
                                OrganizationId = Guid.NewGuid(),
                                Name = q.Name,
                                Name2 = q.ShortName,
                                OrganizationCode = q.OID,
                                Telephone = q.Phone,
                                Status = q.Status == "1" ? 1 : 0,
                                F1 = q.PersonInCharge, // 部门负责人
                                F2 = q.POIdOrgAdmin, //上级部门
                                Source = "MDM",
                                CreateDate = q.CreatedTime.ToOurDateTime(),
                                ModifyDate = q.ModifiedTime.ToOurDateTime()
                            };

                            if (q.OrderCode != null)
                            {
                                if (int.TryParse(q.OrderCode, out int mySortOrder))
                                {
                                    tempOrg.SortCode = mySortOrder;
                                }
                            }
                        }
                        else
                        {
                            tempOrg.Name = q.Name;
                            tempOrg.Name2 = q.ShortName;
                            tempOrg.Telephone = q.Phone;
                            tempOrg.Status = q.Status == "1" ? 1 : 0;
                            tempOrg.F1 = q.PersonInCharge;
                            tempOrg.F2 = q.POIdOrgAdmin;
                            tempOrg.ModifyDate = q.ModifiedTime.ToOurDateTime();
                        }

                        organizations.Add(tempOrg); // 供插入boost数据库

                        _syncManageOrganizations.Add(tempOrg); // 只供后续处理部门负责人，如果不需要更新负责人的，可以自行删除
                    });

                    _dbContext.Boost.Storageable(organizations).ExecuteCommand();
                    Console.WriteLine($"已同步{count * size}笔，总条数{response.Data.Total}");
                }
                else
                {
                    hasNext = false;
                }

                ++page;
            }
        }

        private void UpdateFullPath()
        {
            // 补齐父级部门
            string sql = "update organizations A inner join organizations B	on A.OrganizationCode = B.F2 set B.UpperId = A.OrganizationId	where A.Source='MDM' and B.Source='MDM' and B.F2 != ''";
            _dbContext.Boost.Ado.ExecuteCommand(sql, new List<SugarParameter>());

            var list = _dbContext.Boost.Queryable<OrganizationTable>().Where(q => q.Source == "MDM").ToList();

            var rootInfo = _dbContext.Boost.Queryable<OrganizationTable>().First(q => q.OrganizationCode == "900110496");
            list.ForEach(q =>
            {
                //if (q.OrganizationCode != "900110496")
                {
                    string fullCode = null, fullText = null;
                    q.FullPathCode = $"{rootInfo.OrganizationId}.0{GetId(list, q.OrganizationId, fullCode)}";
                    q.FullPathText = $"{rootInfo.Name}{GetName(list, q.OrganizationId, fullText)}";
                }
                //else 
                //{
                //    q.FullPathCode = rootInfo.OrganizationId.ToString();
                //    q.FullPathText = rootInfo.Name;
                //}
            });

            bool hasNext = true;
            int listCount = (list.Count / 500) + 1;
            while (hasNext)
            {
                for (int i = 0; i < listCount; i++)
                {
                    var test = list.Take(500).ToList();
                    list = list.Skip(500).ToList();
                    _dbContext.Boost.Updateable(test).ExecuteCommand();
                }

                hasNext = false;
            }
        }

        private string GetId(List<OrganizationTable> list, Guid? id, string fullCode)
        {
            var result = list.Find(a => a.OrganizationId == id);

            if (result.UpperId != null)
            {
                fullCode = $"_{result.OrganizationId}.100" + fullCode;
                return GetId(list, result.UpperId, fullCode);
            }

            return fullCode;
        }

        private string GetName(List<OrganizationTable> list, Guid? id, string fullName)
        {
            var result = list.Find(a => a.OrganizationId == id);
            if (result.UpperId != null)
            {
                fullName = $"_{result.Name}" + fullName;
                return GetName(list, result.UpperId, fullName);
            }

            return fullName;
        }
        #endregion

        #region 同步岗位基本信息
        /// <summary>
        /// 同步岗位
        /// </summary>
        private void SyncPositions()
        {
            var url = _appSettingsDto["SystemSettings"]?["SaiWu"]?["GetPositionsUrl"].ToString();
            bool hasNext = true;
            int count = 0, size = 50, page = 1;

            // 把组织读到内存中，方便查询以及提高速度
            var tempOrganizations = _dbContext.Boost.Queryable<OrganizationTable>().Select(q => new OrganizationTable
            {
                OrganizationId = q.OrganizationId,
                OrganizationCode = q.OrganizationCode
            }).ToList();

            while (hasNext)
            {
                count++;
                Thread.Sleep(50);
                if (count > _maxCount)
                {
                    hasNext = false;
                }

                var body = new RequestDto
                {
                    Timestamp = _lastUpdateTime.ToOurString(Clock.ClockType.DateTime),
                    Size = size,
                    Page = page,
                };

                var response = HttpHelper.Post<ResponseDto<PositionResultDto>>(url, body, new HttpRequestHeaderDto { Headers = CreateHeader(), });
                if (response == null || response.ErrorCode != "0")
                {
                    throw new Exception($"读取职位信息记录失败:{response.ErrorMessage}");
                }
                else if (response.Data.Items?.Count > 0)
                {
                    Console.WriteLine($"正在准备同步前{count * size}笔，总条数{response.Data.Total}");
                    List<Position> positions = new List<Position>();

                    response.Data.Items.ForEach(q =>
                    {
                        var tempPosition = _dbContext.Boost.Queryable<Position>().First(z => z.F1 == q.OId);
                        var positionOrganization = tempOrganizations.First(z => z.OrganizationCode == q.OIdOrganization);

                        if (positionOrganization != null && !string.IsNullOrEmpty(q.Code)) //岗位能匹配到岗位才录入BPM，否则认为是脏数据不予理睬
                        {
                            if (tempPosition == null)
                            {
                                tempPosition = new Position
                                {
                                    PositionId = Guid.NewGuid(),
                                    OrganizationId = positionOrganization?.OrganizationId,
                                    PositionCode = q.Code,
                                    Name = q.Name,
                                    Description = "MDM",
                                    IsActive = q.Status == "1",
                                    F1 = q.OId,  // 利用OIDJobPost信息来匹配人员岗位信息
                                };
                            }
                            else
                            {
                                tempPosition.PositionCode = q.Code;
                                tempPosition.Name = q.Name;
                                tempPosition.OrganizationId = positionOrganization?.OrganizationId;
                                tempPosition.Description = "MDM";
                                tempPosition.IsActive = q.Status == "1";
                            }

                            positions.Add(tempPosition);
                        }
                    });

                    _dbContext.Boost.Storageable(positions).ExecuteCommand();
                    Console.WriteLine($"已同步{count * size}笔，总条数{response.Data.Total}");
                }
                else
                {
                    hasNext = false;
                }

                ++page;
            }
        }
        #endregion

        #region 同步人员、绑定人员岗位信息、处理部门负责人
        /// <summary>
        /// 同步人员基本信息（注意，无工号信息）
        /// </summary>
        private void SyncUsers()
        {
            var url = _appSettingsDto["SystemSettings"]?["SaiWu"]?["GetUsersUrl"].ToString();
            bool hasNext = true;
            int count = 0, size = 50, page = 1;
            while (hasNext)
            {
                count++;
                Thread.Sleep(50);
                if (count > _maxCount)
                {
                    hasNext = false;
                }

                var body = new RequestDto
                {
                    Timestamp = _lastUpdateTime.ToOurString(Clock.ClockType.DateTime),
                    Size = size,
                    Page = page,
                };

                var response = HttpHelper.Post<ResponseDto<EmployeeResultDto>>(url, body, new HttpRequestHeaderDto { Headers = CreateHeader(), });
                if (response == null || response.ErrorCode != "0")
                {
                    throw new Exception($"读取人员失败:{response.ErrorMessage}");
                }
                else if (response.Data.Items?.Count > 0)
                {
                    Console.WriteLine($"正在准备同步前{count * size}笔，总条数{response.Data.Total}");
                    // step1、先把人员基本信息（不包括工号，这MDM设计，居然没有唯一标识工号）同步进系统
                    List<User> users = new List<User>();
                    response.Data.Items.ForEach(q =>
                    {
                        var tempUser = _dbContext.Boost.Queryable<User>().First(z => z.F1 == q.UserID);
                        if (tempUser == null)
                        {
                            tempUser = new User
                            {
                                UserId = Guid.NewGuid(),
                                UserLoginId = $"Fake{q.UserID}",
                                UserName = q.Name,
                                Email = q.Email,
                                MobilePhone = q.MobilePhone,
                                WorkNumber = q.UserID, // 借用北森的UserId，以后用这个UserId再去更新LoginId
                                Source = "MDM",
                                Status = 1,
                                CreateDate = q.CreatedTime.ToOurDateTime(),
                                ModifyDate = q.ModifiedTime.ToOurDateTime(),
                                F1 = q.UserID,
                            };
                        }
                        else
                        {
                            tempUser.UserName = q.Name;
                            tempUser.Email = q.Email;
                            tempUser.MobilePhone = q.MobilePhone;
                            tempUser.Status = 1; // q.ActivationState == "1" ? 1 : 0;
                            tempUser.WorkNumber = q.UserID;
                            tempUser.F1 = q.UserID;
                            tempUser.ModifyDate = q.ModifiedTime.ToOurDateTime();
                        }

                        users.Add(tempUser);
                    });

                    _dbContext.Boost.Storageable(users).ExecuteCommand();
                    Console.WriteLine($"已同步{count * size}笔，总条数{response.Data.Total}");
                }
                else
                {
                    hasNext = false;
                }

                ++page;
            }
        }

        /// 人员任职记录（1、先补充工号信息，2、再补充人员岗位信息）
        /// </summary>
        private void SyncUserPositions()
        {
            var url = _appSettingsDto["SystemSettings"]?["SaiWu"]?["GetEmployeeRecsUrl"].ToString();
            bool hasNext = true;
            int count = 0, size = 50, page = 1;

            // 把岗位读到内存中，方便查询以及提高速度
            var tempPositions = _dbContext.Boost.Queryable<Position>().Select(q => new Position
            {
                PositionId = q.PositionId,
                OrganizationId = q.OrganizationId,
                F1 = q.F1,
            }).ToList();

            // 把组织读到内存中，方便查询以及提高速度
            var tempOrganizations = _dbContext.Boost.Queryable<OrganizationTable>().Select(q => new OrganizationTable
            {
                OrganizationId = q.OrganizationId,
                OrganizationCode = q.OrganizationCode,
                FullPathCode = q.FullPathCode,
                FullPathText = q.FullPathText,
            }).ToList();

            while (hasNext)
            {
                count++;
                Thread.Sleep(50);
                if (count > _maxCount)
                {
                    hasNext = false;
                }

                var body = new RequestDto
                {
                    Timestamp = _lastUpdateTime.ToOurString(Clock.ClockType.DateTime),
                    Size = size,
                    Page = page,
                };

                var response = HttpHelper.Post<ResponseDto<EmployeeRecResultDto>>(url, body, new HttpRequestHeaderDto { Headers = CreateHeader(), });

                if (response == null || response.ErrorCode != "0")
                {
                    throw new Exception($"读取人员任职记录失败:{response.ErrorMessage}");
                }
                else if (response.Data.Items?.Count > 0)
                {
                    Console.WriteLine($"正在准备同步前{count * size}笔，总条数{response.Data.Total}");

                    // 先把人员基本信息（不包括工号，MDM的设计是没有唯一标识工号的！！）同步进系统
                    List<User> users = new List<User>();

                    // 再把人员的职位信息补充完毕
                    List<UserPositionRelation> userPositionRelations = new List<UserPositionRelation>();
                    response.Data.Items.ForEach(q =>
                    {
                        // if (q.UserID == "158001292")
                        {
                            // F1存的是MDM主键，这个主键没法直接用，得根据MDM主键计算出工号并更新用户
                            var tempUser = _dbContext.Boost.Queryable<User>().First(z => z.F1 == q.UserID);
                            if (tempUser == null)
                            {
                                //，任职信息里连人名都没有，总不能给个假名吧！！
                            }
                            else
                            {
                                tempUser.UserLoginId = q.JobNumber; // 修订人员的工号
                                tempUser.F2 = q.POIdEmpAdmin; // 直属上级
                                tempUser.ModifyDate = q.ModifiedTime.ToOurDateTime();

                                // 先找到岗位，再决定是新增普通岗位关系、新增主岗关系，还是删除这个岗位关系
                                var tempPosition = tempPositions.Where(z => z.F1 == q.OIdJobPosition).FirstOrDefault();

                                // 此处只做主岗处理，后续需要兼岗还需要重新接入MDM
                                if (tempPosition != null)
                                {
                                    //_dbContext.Boost.Deleteable<UserPositionRelation>().Where(z => z.UserId == tempUser.UserId && z.PositionId == tempPosition.PositionId).ExecuteCommand();
                                    _dbContext.Boost.Deleteable<UserPositionRelation>().Where(z => z.UserId == tempUser.UserId).ExecuteCommand();

                                    if (!q.StdIsDeleted)
                                    {
                                        userPositionRelations.Add(new UserPositionRelation
                                        {
                                            UserPositionRelationId = Guid.NewGuid(),
                                            PositionId = tempPosition.PositionId,
                                            UserId = tempUser.UserId,
                                            PrimaryPosition = q.IsMainJob == "1",
                                            //StartDate = DateTime.MinValue,
                                            //EndDate = DateTime.MaxValue,
                                            IsActive = true,
                                            F1 = "MDM",
                                        });
                                    }

                                    var tempOrg = tempOrganizations.Where(z => z.OrganizationId == tempPosition.OrganizationId).FirstOrDefault();
                                    if (tempOrg != null)
                                    {
                                        tempUser.OrganizationId = tempOrg.OrganizationId;
                                        tempUser.OrganizationName = tempUser.OrganizationName;
                                        tempUser.FullPathCode = tempOrg.FullPathCode;
                                        tempUser.FullPathText = tempOrg.FullPathText;
                                    }
                                }
                            }

                            if (tempUser != null)
                            {
                                users.Add(tempUser);
                                _syncUserUpperIds.Add(tempUser); // 专门处理人员所属上级，如果不需要做处理的，可以自行删除掉
                            }
                        }
                    });

                    _dbContext.Boost.Storageable(users).ExecuteCommand();
                    _dbContext.Boost.Storageable(userPositionRelations).ExecuteCommand();

                    Console.WriteLine($"已同步{count * size}笔，总条数{response.Data.Total}");
                }
                else
                {
                    hasNext = false;
                }

                ++page;
            }
        }

        private void UpdateOrganizationManager()
        {
            var totalCount = _syncManageOrganizations?.Count;
            var processCount = 0;

            var organizationExtends = new List<OrganizationExtend>(); // 补充部门经理
            _syncManageOrganizations.ForEach(q =>
            {
                if (processCount % 100 == 0)
                {
                    Console.WriteLine($"正在准备同步前{processCount}笔，总条数{totalCount}");
                }
                if (!string.IsNullOrEmpty(q.F1))
                {
                    var user = _dbContext.Boost.Queryable<User>().First(z => z.F1 == q.F1);
                    if (user != null)
                    {
                        q.Manager = user.UserId.ToString();

                        var extend = _dbContext.Boost.Queryable<OrganizationExtend>().First(z => z.OrganizationId == q.OrganizationId);
                        if (extend == null)
                        {
                            extend = new OrganizationExtend
                            {
                                OrganizationId = q.OrganizationId,
                                ManagerName = user.UserName // 部门经理名称
                            };
                        }
                        else
                        {
                            extend.ManagerName = user.UserName; // 部门经理名称
                        }

                        organizationExtends.Add(extend);
                    }
                }

                ++processCount;
            });

            _dbContext.Boost.Storageable(_syncManageOrganizations).ExecuteCommand(); // 补充部门经理ID
            _dbContext.Boost.Storageable(organizationExtends).ExecuteCommand(); // 补充部门经理名称
        }

        private void UpdateUpperIds()
        {
            var totalCount = _syncUserUpperIds?.Count;
            var processCount = 0;

            List<User> tempUsers = new List<User>(); //内存暂存

            _syncUserUpperIds.ForEach(q =>
            {
                if (processCount % 100 == 0)
                {
                    Console.WriteLine($"正在准备同步前{processCount}笔，总条数{totalCount}");
                }

                if (!string.IsNullOrEmpty(q.F2))
                {
                    if (tempUsers.Any(x => x.F1 == q.F2))
                    {
                        // 内存有就从内存取
                        q.UpperUserId = tempUsers.Where(x => x.F1 == q.F2).FirstOrDefault()?.UserId;
                    }
                    else
                    {
                        var user = _dbContext.Boost.Queryable<User>().First(z => z.F1 == q.F2);
                        if (user != null)
                        {
                            tempUsers.Add(user);
                            q.UpperUserId = user.UserId;
                        }
                    }
                }

                ++processCount;
            });

            _dbContext.Boost.Storageable(_syncUserUpperIds).ExecuteCommand();
        }
        #endregion
    }
}