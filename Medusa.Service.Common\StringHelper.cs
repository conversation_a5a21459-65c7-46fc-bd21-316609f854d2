using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Microsoft.International.Converters.PinYinConverter;

namespace Medusa.Service.Common
{
    /// <summary>
    /// 字符串帮助类
    /// </summary>
    public static class StringHelper
    {

        /// <summary>
        /// 中文转拼音首字母(如果有多音字，以英文分号分隔)
        /// </summary>
        /// <param name="chineseStr"></param>
        /// <returns></returns>
        public static string ConvertChineseToPinyinFirstWord(this string chineseStr){
            if (string.IsNullOrWhiteSpace(chineseStr))
            {
                return string.Empty;
            }
            const string chineseReg = "^[\\u4E00-\\u9FA5]+$";
            var firstDict = new List<List<char>>();
            foreach (var itemChar in chineseStr)
            {
                //过滤非汉字的字符，直接返回
                var reg = new Regex(chineseReg);
                if (!reg.IsMatch(itemChar.ToString()))
                {
                    firstDict.Add(new List<char>{itemChar});
                }
                else
                {
                    var chineseChar = new ChineseChar(itemChar);
                    var pyStr = chineseChar.Pinyins.Where(e => !string.IsNullOrWhiteSpace(e)).Select(e => e.ToLower().First()).Distinct().ToList();
                    firstDict.Add(pyStr);
                }
            }

            var strList = new List<string>{""};
            strList = firstDict.Aggregate(strList, (current, t) => t.SelectMany(e => current.Select(f => $"{f}{e}")).ToList());
            var str = string.Join(";", strList);
            return str;
        }
    }
}