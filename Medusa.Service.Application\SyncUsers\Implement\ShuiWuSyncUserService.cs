﻿using Medusa.Service.Common;
using Medusa.Service.Core.Entities.Platform;
using Medusa.Service.Core.ORM;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MT.Enterprise.Utils;
using MT.Enterprise.Utils.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;

namespace Medusa.Service.Application.SyncUsers
{
    /// <summary>
    /// 同步组织人员服务
    /// </summary>
    public class ShuiWuSyncUserService : ISyncUserService
    {
        private readonly MyDbContext _dbContext;
        private readonly JObject _appSettingsDto;
        private DateTime _lastUpdateTime;
        private DateTime _thisUpdateTime;
        private string _accessToken;
        private readonly ILogger _logger;
        private List<Dtos.ShuiWu.OrganizationResultDto> _organizationList = new List<Dtos.ShuiWu.OrganizationResultDto>();
        private List<Dtos.ShuiWu.PositionResultDto> _positionList = new List<Dtos.ShuiWu.PositionResultDto>();
        private List<Dtos.ShuiWu.EmployeeResultDto> _employeeList = new List<Dtos.ShuiWu.EmployeeResultDto>();

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serviceProvider">serviceProvider</param>
        public ShuiWuSyncUserService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            _appSettingsDto = serviceProvider.GetService<IMemoryCache>().Get<JObject>("AppSettings");
            _logger = serviceProvider.GetService<ILogger<ShuiWuSyncUserService>>();
        }

        public string SystemName => "ShuiWu";

        /// <summary>
        /// 同步入口
        /// </summary>
        public void SyncOrgAndUsersAsync()
        {
            // 同步方案：http://confluence.movit-tech.com:1020/pages/viewpage.action?pageId=29886962

            try
            {
                _thisUpdateTime = DateTime.Now;
                GetLastSyncDateTime();

                Console.WriteLine(" 请求主数据Token开始！");
                GetAccessTokenAsync();
                if (string.IsNullOrWhiteSpace(_accessToken))
                {
                    Console.WriteLine($"未获取到token，停止请求。");
                    return;
                }

                Console.WriteLine(" 同步组织开始！");
                SyncOrganizations();

                Console.WriteLine(" 补充组织全路径开始！");
                UpdateFullPath();

                Console.WriteLine(" 同步岗位基本信息开始！");
                SyncPositions();

                Console.WriteLine(" 同步人员开始！");
                SyncUsers();

                Console.WriteLine(" 同步人员岗位开始！");
                SyncUserPositions();

                Console.WriteLine(" 同步组织负责人开始！");
                SyncOrganizationManager();

                UpdateLastSyncDateTime(_thisUpdateTime);
                Console.WriteLine(" 同步结束！");

                Console.WriteLine(" 刷新缓存开始！");
                RefreshCache();
                Console.WriteLine(" 刷新缓存结束！");
            }
            catch (Exception ex)
            {
                Console.Write($"错误提示：{ex.Message}");
            }
        }

        #region 记录上一次更新时间相关
        /// <summary>
        /// 获取最后一次同步时间
        /// </summary>
        private void GetLastSyncDateTime()
        {
            var lastSyncParam = _dbContext.Boost.Queryable<ConfigSetting>().First(q => q.Key == "LastSyncDate");
            _lastUpdateTime = string.IsNullOrEmpty(lastSyncParam?.Value) ? DateTime.MinValue : DateTime.Parse(lastSyncParam.Value);
            Console.WriteLine($"上一次同步时间是：{_lastUpdateTime}");
        }

        private void UpdateLastSyncDateTime(DateTime date)
        {
            var lastSyncParam = _dbContext.Boost.Queryable<ConfigSetting>().First(q => q.Key == "LastSyncDate");
            if (lastSyncParam != null)
            {
                lastSyncParam.Value = date.ToOurString(Clock.ClockType.DateTime);
                lastSyncParam.Group = "SyncMDM";
                lastSyncParam.CreatorName = "自动同步";
                lastSyncParam.CreatorTime = DateTime.Now;
            }
            else
            {
                lastSyncParam = new ConfigSetting
                {
                    Key = "LastSyncDate",
                    Value = date.ToOurString(Clock.ClockType.DateTime),
                    Group = "SyncMDM",
                    CreatorName = "自动同步",
                    CreatorTime = DateTime.Now,
                };
            }

            _dbContext.Boost.Storageable(lastSyncParam).ExecuteCommand();
        }

        #endregion

        #region 主数据Token请求相关

        /// <summary>
        /// 获取Token
        /// </summary>
        private void GetAccessTokenAsync()
        {
            var url = _appSettingsDto["SystemSettings"]?["ShuiWu"]?["GetTokenUrl"]?.ToString();
            var tokenCode = _appSettingsDto["SystemSettings"]?["ShuiWu"]?["TokenCode"]?.ToString();
            var tokenClientId = _appSettingsDto["SystemSettings"]?["ShuiWu"]?["TokenClientId"]?.ToString();
            if (string.IsNullOrWhiteSpace(url))
            {
                throw new Exception("GetAccessTokenAsync：url为空");
            }
            
            var timestamp = new DateTimeOffset(DateTime.Now).ToUnixTimeMilliseconds();

            url = $"{url}?code={tokenCode}&type=0&timestamp={timestamp}&sign={Crypto.Base64Encrypt(Crypto.MD5($"{tokenCode}{timestamp}Qazxsw4321", false))}&clientId={tokenClientId}";

            string result;
            using (var httpClient = new HttpClient())
            {
                var response = httpClient.GetAsync(url).Result;
                result = response.Content.ReadAsStringAsync().Result;
            }
            _logger.LogInformation($"GetAccessTokenAsync：result：{result}");

            if (string.IsNullOrWhiteSpace(result))
            {
                throw new Exception("GetAccessTokenAsync：请求Token为空");
            }
            
            var jsonObj = JsonConvert.DeserializeObject<JObject>(result);
            if (jsonObj == null || jsonObj["result"]?.ToString() != "true" || string.IsNullOrWhiteSpace(jsonObj["data"]?["access_token"]?.ToString()))
            {
                throw new Exception("GetAccessTokenAsync：result反序列化为null或result不为true或access_token为空");
            }

            _accessToken = jsonObj["data"]?["access_token"]?.ToString();
        }

        /// <summary>
        /// 拼接请求头
        /// </summary>
        /// <returns></returns>
        private Dictionary<string, string> CreateHeader()
        {
            if (string.IsNullOrWhiteSpace(_accessToken))
            {
                throw new Exception("空令牌");
            }
            
            var dic = new Dictionary<string, string> { { "Authorization", $"Bearer {_accessToken}" } };
            return dic;
        }
        #endregion

        #region 同步组织相关
        /// <summary>
        /// 同步组织
        /// </summary>
        private void SyncOrganizations()
        {
            var url = _appSettingsDto["SystemSettings"]?["ShuiWu"]?["GetOrganizationsUrl"]?.ToString();
            url = $"{url}?startDate={_lastUpdateTime:yyyy-MM-dd HH:mm:ss}&endDate={_thisUpdateTime:yyyy-MM-dd HH:mm:ss}";
            var result = HttpHelper.Get<Dtos.ShuiWu.ResponseDto<Dtos.ShuiWu.OrganizationResultDto>>(url, new HttpRequestHeaderDto { Headers = CreateHeader(), });
            if (!(result is { result: "true" }))
            {
                throw new Exception("SyncOrganizations：result反序列化为null或result不为true");
            }

            if (result.data.Count == 0) return;
            Console.WriteLine($"正在准备同步前，总条数{result.data.Count}");
            _organizationList = result.data;
            
            // step1、先把基本信息（不包括上级关系、全路径）同步进系统
            var organizations = new List<OrganizationTable>();
            var organizationExtends = new List<OrganizationExtend>();
            foreach (var q in _organizationList)
            {
                var tempOrg = _dbContext.Boost.Queryable<OrganizationTable>().First(z => z.OrganizationCode == q.officeCode);
                if (tempOrg == null)
                {
                    tempOrg = new OrganizationTable
                    {
                        OrganizationId = Guid.NewGuid(),
                        Name = q.officeName,
                        OrganizationCode = q.officeCode,
                        Status = q.status == "0" ? 1 : 0,
                        Source = "MDM",
                        CreateDate = q.createDate,
                        ModifyDate = q.updateDate,
                        SortCode = q.treeSort,
                        F2 = q.parentCode,
                        F1 = q.shuiwuDeptId
                    };
                    organizationExtends.Add(new OrganizationExtend()
                    {
                        OrganizationId = tempOrg.OrganizationId,
                        DomainId = _appSettingsDto["SystemSettings"]?["ShuiWu"]?["DefaultDomainId"]?.ToString().ToOurGuid(),
                        DomainName = _appSettingsDto["SystemSettings"]?["ShuiWu"]?["DefaultDomainName"]?.Value<string>(),
                        DomainLevelId = _appSettingsDto["SystemSettings"]?["ShuiWu"]?["DefaultDomainLevelId"]?.ToString().ToOurGuid(),
                        DomainLevelName = _appSettingsDto["SystemSettings"]?["ShuiWu"]?["DefaultDomainLevelName"]?.Value<string>(),
                        DomainLevelCode = _appSettingsDto["SystemSettings"]?["ShuiWu"]?["DefaultDomainLevelCode"]?.Value<string>(),
                        Weight = _appSettingsDto["SystemSettings"]?["ShuiWu"]?["DefaultWeight"]?.Value<int>(),
                    });
                }
                else
                {
                    tempOrg.Name = q.officeName;
                    tempOrg.Status = q.status == "0" ? 1 : 0;
                    tempOrg.ModifyDate = q.updateDate;
                    tempOrg.SortCode = q.treeSort;
                    tempOrg.F2 = q.parentCode;
                    tempOrg.F1 = q.shuiwuDeptId;
                }
                
                organizations.Add(tempOrg); // 供插入boost数据库
            }

            _dbContext.Boost.Storageable(organizations).ExecuteCommand();
            _dbContext.Boost.Storageable(organizationExtends).ExecuteCommand();
            Console.WriteLine($"已同步，总条数{result.data.Count}");
        }

        private void UpdateFullPath()
        {
            // 补齐父级部门
            string sql = "update organizations A LEFT  JOIN organizations B	on B.OrganizationCode = A.F2 set A.UpperId = B.OrganizationId where A.Source='MDM' and (B.Source='MDM' or b.OrganizationId is null)";
            _dbContext.Boost.Ado.ExecuteCommand(sql, new List<SugarParameter>());

            var list = _dbContext.Boost.Queryable<OrganizationTable>().ToList();
            var orgExtList = _dbContext.Boost.Queryable<OrganizationExtend>().ToList();
            
            list.ForEach(q =>
            {
                var path = GetIdAndName(list, orgExtList, q.OrganizationId);
                q.FullPathCode = path.Item1;
                q.FullPathText = path.Item2;
            });

            bool hasNext = true;
            int listCount = (list.Count / 500) + 1;
            while (hasNext)
            {
                for (int i = 0; i < listCount; i++)
                {
                    var test = list.Take(500).ToList();
                    list = list.Skip(500).ToList();
                    _dbContext.Boost.Updateable(test).ExecuteCommand();
                }

                hasNext = false;
            }
        }

        /// <summary>
        /// 递归查出路径
        /// </summary>
        /// <param name="list"></param>
        /// <param name="orgExtList"></param>
        /// <param name="id"></param>
        /// <returns>Item1：id路径，Item2：名称路径</returns>
        private Tuple<string, string> GetIdAndName(List<OrganizationTable> list, List<OrganizationExtend> orgExtList, Guid? id)
        {
            var result = list.Find(a => a.OrganizationId == id);
            var weight = orgExtList.Find(a => a.OrganizationId == id)?.Weight ??
                         _appSettingsDto["SystemSettings"]?["ShuiWu"]?["DefaultWeight"]?.Value<int>();
            
            if (result == null)
            {
                return new Tuple<string, string>(string.Empty, string.Empty);
            }

            if (result.UpperId == null)
            {
                return new Tuple<string, string>($"{result.OrganizationId}.{weight}", result.Name);
            }

            var path = GetIdAndName(list, orgExtList, result.UpperId);
            return new Tuple<string, string>($"{path.Item1}_{result.OrganizationId}.{weight}", $"{path.Item2}_{result.Name}");
        }

        #endregion

        #region 同步岗位基本信息
        /// <summary>
        /// 同步岗位
        /// </summary>
        private void SyncPositions()
        {
            var url = _appSettingsDto["SystemSettings"]?["ShuiWu"]?["GetPositionsUrl"]?.ToString();
            url = $"{url}?startDate=&endDate=";
            var result = HttpHelper.Get<Application.SyncUsers.Dtos.ShuiWu.ResponseDto<Application.SyncUsers.Dtos.ShuiWu.PositionResultDto>>(url, new HttpRequestHeaderDto { Headers = CreateHeader(), });
            if (!(result is { result: "true" }))
            {
                throw new Exception("SyncPositions：result反序列化为null或result不为true");
            }

            if (result.data.Count == 0) return;

            _positionList = result.data;
        }
        #endregion

        #region 同步人员、绑定人员岗位信息、处理部门负责人
        /// <summary>
        /// 同步人员基本信息（注意，无工号信息）
        /// </summary>
        private void SyncUsers()
        {
            var url = _appSettingsDto["SystemSettings"]?["ShuiWu"]?["GetUsersUrl"]?.ToString();
            url = $"{url}?startDate={_lastUpdateTime:yyyy-MM-dd HH:mm:ss}&endDate={_thisUpdateTime:yyyy-MM-dd HH:mm:ss}";
            var result = HttpHelper.Get<Application.SyncUsers.Dtos.ShuiWu.ResponseDto<Application.SyncUsers.Dtos.ShuiWu.EmployeeResultDto>>(url, new HttpRequestHeaderDto { Headers = CreateHeader(), });
            if (!(result is { result: "true" }))
            {
                throw new Exception("SyncUsers：result反序列化为null或result不为true");
            }

            if (result.data.Count == 0) return;
            Console.WriteLine($"正在准备同步前，总条数{result.data.Count}");

            _employeeList = result.data;
            // step1、先把人员基本信息（不包括工号，这MDM设计，居然没有唯一标识工号）同步进系统
            List<User> users = new List<User>();
            result.data.ForEach(q =>
            {
                var tempUser = _dbContext.Boost.Queryable<User>().First(z => z.WorkNumber == q.userCode);
                var org = _dbContext.Boost.Queryable<OrganizationTable>().Select(z => new OrganizationTable
                    {
                        OrganizationId = z.OrganizationId,
                        OrganizationCode = z.OrganizationCode,
                        Name = z.Name,
                        FullPathCode = z.FullPathCode,
                        FullPathText = z.FullPathText,
                    })
                    .First(z => z.OrganizationCode == q.officeCode);
                var pinyinFirstWord = q.userName.ConvertChineseToPinyinFirstWord();
                pinyinFirstWord = pinyinFirstWord.Length > 100 ? pinyinFirstWord.Substring(0, 100) : pinyinFirstWord;
                if (tempUser == null)
                {
                    tempUser = new User
                    {
                        UserId = Guid.NewGuid(),
                        UserLoginId = q.loginCode,
                        UserName = q.userName,
                        PinyinFirstWord = pinyinFirstWord,
                        Email = q.email,
                        MobilePhone = q.mobile,
                        Status = q.status == "0" ? 1 : 0,
                        SortCode = q.userWeight ?? 0,
                        OrganizationId = org.OrganizationId,
                        OrganizationName = org.Name,
                        FullPathCode = org.FullPathCode,
                        FullPathText = org.FullPathText,
                        WorkNumber = q.userCode,
                        Source = "MDM",
                        CreateDate = q.createDate,
                        ModifyDate = q.updateDate,
                        Password = _appSettingsDto["SystemSettings"]?["ShuiWu"]?["DefaultPassword"]?.Value<string>(),
                        F1 = q.shuiwuUserId
                    };
                }
                else
                {
                    tempUser.UserName = q.userName;
                    tempUser.PinyinFirstWord = pinyinFirstWord;
                    tempUser.UserLoginId = q.loginCode;
                    tempUser.Email = q.email;
                    tempUser.MobilePhone = q.mobile;
                    tempUser.Status = q.status == "0" ? 1 : 0;
                    tempUser.SortCode = q.userWeight ?? 0;
                    tempUser.OrganizationId = org.OrganizationId;
                    tempUser.OrganizationName = org.Name;
                    tempUser.FullPathCode = org.FullPathCode;
                    tempUser.FullPathText = org.FullPathText;
                    tempUser.ModifyDate = q.updateDate;
                    tempUser.F1 = q.shuiwuUserId;
                }

                users.Add(tempUser);
                // 冗余BPM的Userid
                q.BpmUserId = tempUser.UserId;
            });

            _dbContext.Boost.Storageable(users).ExecuteCommand();
            Console.WriteLine($"已同步，总条数{result.data.Count}");
        }

        /// <summary>
        /// 人员任职记录
        /// </summary>
        /// <exception cref="Exception"></exception>
        private void SyncUserPositions()
        {
            if (_positionList == null || _positionList.Count == 0)
            {
                Console.WriteLine($"SyncUserPositions:岗位列表为空");
                return;
            }
            if (_employeeList == null || _employeeList.Count == 0)
            {
                Console.WriteLine($"SyncUserPositions:用户列表为空");
                return;
            }
            // 把岗位读到内存中，方便查询以及提高速度
            var bpmPositions = _dbContext.Boost.Queryable<Position>().ToList();
            var bpmUserPositionRelations = _dbContext.Boost.Queryable<UserPositionRelation>().ToList();

            // 把组织读到内存中，方便查询以及提高速度
            var bpmOrganizations = _dbContext.Boost.Queryable<OrganizationTable>().Select(q => new OrganizationTable
            {
                OrganizationId = q.OrganizationId,
                OrganizationCode = q.OrganizationCode,
                FullPathCode = q.FullPathCode,
                FullPathText = q.FullPathText,
            }).ToList();
            
            // 如果没有主岗，添加默认主岗测试
            _employeeList.ForEach(q =>
            {
                if (string.IsNullOrWhiteSpace(q.postCode))
                {
                    q.postCode = "user";
                }
            });

            // 定义修复修改的数据集合
            List<UserPositionRelation> userPositionRelations = new List<UserPositionRelation>();
            List<Position> positions = new List<Position>();
            _employeeList.ForEach(q =>
            {
                var bpmRelations = bpmUserPositionRelations.Where(z => z.UserId == q.BpmUserId).ToList();
                // 默认先都置为无效，后续再打开
                bpmRelations.ForEach(z =>
                {
                    z.IsActive = false;
                    z.PrimaryPosition = false;
                });

                #region 主岗

                var mainPositionCode = string.Empty;
                if (!string.IsNullOrWhiteSpace(q.officeCode) && !string.IsNullOrWhiteSpace(q.postCode))
                {
                    mainPositionCode = $"{q.userCode}_{q.postCode}_{q.officeCode}";
                    var bpmOrg = bpmOrganizations.FirstOrDefault(z => z.OrganizationCode == q.officeCode);
                    var position = _positionList.FirstOrDefault(z => z.postCode == q.postCode);
                    var bpmPosition = bpmPositions.FirstOrDefault(z => z.PositionCode == mainPositionCode && z.OrganizationId == bpmOrg?.OrganizationId);
                    // 处理主岗的岗位表
                    if (bpmPosition == null)
                    {
                        if (bpmOrg != null && position != null)
                        {
                            bpmPosition = new Position()
                            {
                                PositionId = Guid.NewGuid(),
                                OrganizationId = bpmOrg.OrganizationId,
                                PositionCode = mainPositionCode,
                                Name = position.postName,
                                Description = "MDM",
                                IsActive = true,
                            };
                        }
                        else
                        {
                            Console.WriteLine($"SyncUserPositions:{q.userCode}:bpmOrg||position为空");
                        }
                    }
                    else
                    {
                        bpmPosition.Name = position?.postName;
                    }

                    if (bpmPosition != null)
                    {
                        positions.Add(bpmPosition);

                        #region 处理主岗的关系表

                        if (bpmRelations.Any(z => z.PositionId == bpmPosition.PositionId))
                        {
                            foreach (var item in bpmRelations.Where(z => z.PositionId == bpmPosition.PositionId))
                            {
                                item.IsActive = true;
                                item.PrimaryPosition = true;
                            }
                        }
                        else
                        {
                            bpmRelations.Add(new UserPositionRelation()
                            {
                                UserPositionRelationId = Guid.NewGuid(),
                                UserId = q.BpmUserId,
                                PositionId = bpmPosition.PositionId,
                                PrimaryPosition = true,
                                IsActive = true
                            });
                        }

                        #endregion
                    }
                }


                #endregion

                #region 兼岗

                if (q.deputyPostList.Any())
                {
                    foreach (var x in q.deputyPostList)
                    {
                        var otherPositionCode = $"{q.userCode}_{x.postCode}_{x.officeCode}";
                        if (!string.IsNullOrWhiteSpace(mainPositionCode) && mainPositionCode == otherPositionCode)
                        {
                            continue;
                        }
                        var bpmPosition = bpmPositions.FirstOrDefault(z => z.PositionCode == otherPositionCode);
                        var bpmOrg = bpmOrganizations.FirstOrDefault(z => z.OrganizationCode == x.officeCode);
                        var position = _positionList.FirstOrDefault(z => z.postCode == x.postCode);
                        // 处理兼岗的岗位表
                        if (bpmPosition == null)
                        {
                            if (bpmOrg != null && position != null)
                            {
                                bpmPosition = new Position()
                                {
                                    PositionId = Guid.NewGuid(),
                                    OrganizationId = bpmOrg.OrganizationId,
                                    PositionCode = otherPositionCode,
                                    Name = position.postName,
                                    Description = "MDM",
                                    IsActive = true,
                                };
                            }
                            else
                            {
                                Console.WriteLine($"SyncUserPositions:{q.userCode}:bpmOrg||position为空");
                            }
                        }
                        else
                        {
                            bpmPosition.Name = position?.postName;
                        }

                        if (bpmPosition != null)
                        {
                            positions.Add(bpmPosition);

                            #region 处理兼岗关系表

                            if (bpmRelations.Any(z => z.PositionId == bpmPosition.PositionId))
                            {
                                foreach (var item in bpmRelations.Where(z => z.PositionId == bpmPosition.PositionId))
                                {
                                    item.IsActive = true;
                                }
                            }
                            else
                            {
                                bpmRelations.Add(new UserPositionRelation()
                                {
                                    UserPositionRelationId = Guid.NewGuid(),
                                    UserId = q.BpmUserId,
                                    PositionId = bpmPosition.PositionId,
                                    PrimaryPosition = false,
                                    IsActive = true
                                });
                            }

                            #endregion
                        }
                    }
                }

                #endregion
                
                userPositionRelations.AddRange(bpmRelations);
            });

            _dbContext.Boost.Storageable(positions).ExecuteCommand();
            _dbContext.Boost.Storageable(userPositionRelations).ExecuteCommand();

            Console.WriteLine($"已同步，总条数{_employeeList.Count},position条数：{positions.Count},userPositionRelation条数:{userPositionRelations.Count}");
        }

        /// <summary>
        /// 同步组织负责人、分管领导
        /// </summary>
        private void SyncOrganizationManager()
        {
            if (_organizationList == null || _organizationList.Count == 0)
            {
                Console.WriteLine($"SyncOrganizationManager:组织列表为空");
                return;
            }
            
            _organizationList.ForEach(q =>
            {
                var organization = _dbContext.Boost.Queryable<OrganizationTable>().First(z => z.OrganizationCode == q.officeCode);
                if (organization == null) return;
                var organizationExtend = _dbContext.Boost.Queryable<OrganizationExtend>().First(z => z.OrganizationId == organization.OrganizationId);
                if (organizationExtend == null) return;
                {
                    // 先默认负责人、分管领导为空
                    organizationExtend.ManagerIds = string.Empty;
                    organizationExtend.ManagerNames = string.Empty;
                    organizationExtend.FGFZIds = string.Empty;
                    organizationExtend.FGFZNames = string.Empty;

                    // 负责人
                    if (!string.IsNullOrWhiteSpace(q.leaderCodes))
                    {
                        var userIdList = new List<Guid>();
                        var userNameList = new List<string>();
                        foreach (var item in q.leaderCodes.Split(",", StringSplitOptions.RemoveEmptyEntries))
                        {
                            var tempUser = _dbContext.Boost.Queryable<User>().First(z => z.WorkNumber == item);
                            if (tempUser == null) continue;
                            userIdList.Add(tempUser.UserId);
                            userNameList.Add(tempUser.UserName);
                        }
                        organizationExtend.ManagerIds = string.Join(";", userIdList.Select(e => e.ToString()).ToList());
                        organizationExtend.ManagerNames = string.Join(";", userNameList);
                    }
                    
                    // 分管领导
                    if (!string.IsNullOrWhiteSpace(q.branchLeaderCodes))
                    {
                        var userIdList = new List<Guid>();
                        var userNameList = new List<string>();
                        foreach (var item in q.branchLeaderCodes.Split(",", StringSplitOptions.RemoveEmptyEntries))
                        {
                            var tempUser = _dbContext.Boost.Queryable<User>().First(z => z.WorkNumber == item);
                            if (tempUser == null) continue;
                            userIdList.Add(tempUser.UserId);
                            userNameList.Add(tempUser.UserName);
                        }
                        organizationExtend.FGFZIds = string.Join(";", userIdList.Select(e => e.ToString()).ToList());
                        organizationExtend.FGFZNames = string.Join(";", userNameList);
                    }

                    _dbContext.Boost.Updateable<OrganizationExtend>(new
                        {
                            organizationExtend.FGFZIds,
                            organizationExtend.FGFZNames,
                            organizationExtend.ManagerIds,
                            organizationExtend.ManagerNames
                        })
                        .Where(z => z.OrganizationId == organization.OrganizationId).ExecuteCommand();
                }
            });
            Console.WriteLine($"已同步，总条数：{_organizationList.Count}");
        }
        #endregion

        #region 刷新缓存
        string[] cacheList = new string[] { "matrix-role-relations", "users", "common-roles", "business-types", "holidays", "tripartite-systems", "message-templates", "organizations", "common-role-user-relations", "user-organizations", "organization-managers", "domains", "matrix-dimension-relations", "user-authorization-processes", "bizDictionaries" };

        public void RefreshCache()
        {
            var refreshCacheUrl = _appSettingsDto["SystemSettings"]?["ShuiWu"]?["RefreshCacheUrl"]?.ToString();
            var processMapCacheUrl = _appSettingsDto["SystemSettings"]?["ShuiWu"]?["ProcessMapCacheUrl"]?.ToString();
            using (var httpClient = new HttpClient())
            {
                foreach (var item in cacheList)
                {
                    var data = httpClient.PostAsync(refreshCacheUrl + "/refresh-cache/" + item, null).Result.Content.ReadAsStringAsync().Result;
                }
                var init = httpClient.PostAsync(refreshCacheUrl + "/init-cache", null).Result.Content.ReadAsStringAsync().Result;
                var content = new StringContent(new List<Guid>().ToOurJsonString(), Encoding.UTF8, "application/json");
                var processMap = httpClient.PostAsync(processMapCacheUrl, content).Result.Content.ReadAsStringAsync().Result;
            }
        }
        #endregion
    }
}