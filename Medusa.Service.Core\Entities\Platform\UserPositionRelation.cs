using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Core.Entities.Platform
{
    /// <summary>
    /// 岗位人员关系
    /// </summary>
    [EntityTable("UserPositionRelations", "岗位人员关系")]
    public class UserPositionRelation
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? UserPositionRelationId { get; set; }

        /// <summary>
        /// 岗位Id
        /// </summary>
        [EntityColumn(ColumnDescription = "岗位Id", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public Guid? PositionId { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        [EntityColumn(ColumnDescription = "用户Id", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public Guid? UserId { get; set; }

        /// <summary>
        /// 是否主岗
        /// </summary>
        [EntityColumn(ColumnDescription = "是否主岗", IsNullable = true)]
        public bool? PrimaryPosition { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        [EntityColumn(ColumnDescription = "开始日期", ColumnDataType = "datetime(3),datetime", IsNullable = true)]
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        [EntityColumn(ColumnDescription = "结束日期", ColumnDataType = "datetime(3),datetime", IsNullable = true)]
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 是否有效
        /// </summary>
        [EntityColumn(ColumnDescription = "是否有效", IsNullable = true)]
        public bool? IsActive { get; set; }

        /// <summary>
        /// F1
        /// </summary>
        [EntityColumn(ColumnDescription = "F1", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string F1 { get; set; }

        /// <summary>
        /// F2
        /// </summary>
        [EntityColumn(ColumnDescription = "F2", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string F2 { get; set; }

        /// <summary>
        /// F3
        /// </summary>
        [EntityColumn(ColumnDescription = "F3", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string F3 { get; set; }
    }
}
