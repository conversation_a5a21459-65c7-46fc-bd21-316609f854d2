﻿using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Core.Entities.Platform
{
    /// <summary>
    /// 组织扩展表
    /// </summary>
    [EntityTable("organizationsext","组织扩展表")]
    public class OrganizationExtend
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid OrganizationId { get; set; }

        /// <summary>
        /// 业态
        /// </summary>
        [EntityColumn(ColumnDescription = "业态", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public Guid? DomainId { get; set; }

        /// <summary>
        /// 业态名称
        /// </summary>
        [EntityColumn(ColumnDescription = "业态层级名称", ColumnDataType = "nvarchar(50)", IsNullable = true)]
        public string DomainName { get; set; }

        /// <summary>
        /// 业态层级
        /// </summary>
        [EntityColumn(ColumnDescription = "业态层级", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public Guid? DomainLevelId { get; set; }

        /// <summary>
        /// 业态层级名称
        /// </summary>
        [EntityColumn(ColumnDescription = "业态层级名称", ColumnDataType = "nvarchar(50)", IsNullable = true)]
        public string DomainLevelName { get; set; }

        /// <summary>
        /// 业态编码
        /// </summary>
        [EntityColumn(ColumnDescription = "业态编码", ColumnDataType = "nvarchar(50)", IsNullable = true)]
        public string DomainLevelCode { get; set; }

        /// <summary>
        /// 业态
        /// </summary>
        [EntityColumn(ColumnDescription = "所属业态层级权重", IsNullable = true)]
        public int? Weight { get; set; }

        /// <summary>
        /// 所属业态层级等级Id
        /// </summary>
        [EntityColumn(ColumnDescription = "所属业态层级等级Id", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public Guid? CLevelId { get; set; }

        /// <summary>
        /// 所属业态层级等级名
        /// </summary>
        [EntityColumn(ColumnDescription = "所属业态层级等级名", ColumnDataType = "nvarchar(50)", IsNullable = true)]
        public string CLevelName { get; set; }

        /// <summary>
        /// 分管领导
        /// </summary>
        [EntityColumn(ColumnDescription = "分管领导", ColumnDataType = "varchar(500)", IsNullable = true)]
        public string FGFZId { get; set; }

        /// <summary>
        /// 分管领导名称
        /// </summary>
        [EntityColumn(ColumnDescription = "分管领导名称", ColumnDataType = "nvarchar(50)", IsNullable = true)]
        public string FGFZName { get; set; }

        /// <summary>
        /// 部门负责人名称
        /// </summary>
        [EntityColumn(ColumnDescription = "部门负责人名称", ColumnDataType = "nvarchar(50)", IsNullable = true)]
        public string ManagerName { get; set; }
        
        /// <summary>
        /// 分管领导的ID(多人)
        /// </summary>
        [EntityColumn(ColumnDescription = "分管领导的ID(多人)", ColumnDataType = "varchar(500)", IsNullable = true)]
        public string FGFZIds { get; set; }

        /// <summary>
        /// 分管领导人名字(多人)
        /// </summary>
        [EntityColumn(ColumnDescription = "分管领导人名字(多人)", ColumnDataType = "nvarchar(50)", IsNullable = true)]
        public string FGFZNames { get; set; }
        
        /// <summary>
        /// 部门负责人ID(多人)
        /// </summary>
        [EntityColumn(ColumnDescription = "部门负责人ID(多人)", ColumnDataType = "varchar(500)", IsNullable = true)]
        public string ManagerIds { get; set; }

        /// <summary>
        /// 部门负责人名字(多人)
        /// </summary>
        [EntityColumn(ColumnDescription = "部门负责人名字(多人)", ColumnDataType = "nvarchar(50)", IsNullable = true)]
        public string ManagerNames { get; set; }
    }
}
