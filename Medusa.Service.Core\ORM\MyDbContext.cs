using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Core.ORM
{
    /// <summary>
    /// DbContext 访问类
    /// </summary>
    public class MyDbContext
    {
        readonly IDbContext _dbContext;

        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="dbContext">注入 DbContext</param>
        public MyDbContext(IDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// Boost
        /// </summary>
        public MultipleClient Boost
        {
            get { return _dbContext["boost"]; }
        }

        /// <summary>
        /// sourcefrom
        /// </summary>
        public MultipleClient SourceFrom
        {
            get { return _dbContext["sourcefrom"]; }
        }

        /// <summary>
        /// Log
        /// </summary>
        public MultipleClient Log
        {
            get { return _dbContext["Log"]; }
        }
    }
}
