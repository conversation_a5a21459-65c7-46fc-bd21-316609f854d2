using System;
using System.Collections.Generic;
using System.Text;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Core.Entities
{
    /// <summary>
    /// 基础实体
    /// </summary>
    public class BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid Id { get; set; }

        /// <summary>
        /// 1=有效；0=无效
        /// </summary>
        [EntityColumn(ColumnDescription = "1=有效；0=无效", IsNullable = true)]
        public int? Status { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        [EntityColumn(ColumnDescription = "创建人Id", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public Guid? CreateUserId { get; set; }

        /// <summary>
        /// 创建人名称
        /// </summary>
        [EntityColumn(ColumnDescription = "创建人名称", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string CreateUserName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [EntityColumn(ColumnDescription = "创建时间", ColumnDataType = "datetime(3),datetime", IsNullable = true)]
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 修改人Id
        /// </summary>
        [EntityColumn(ColumnDescription = "修改人Id", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = true)]
        public Guid? ModifyUserId { get; set; }

        /// <summary>
        /// 修改人名称
        /// </summary>
        [EntityColumn(ColumnDescription = "修改人名称", ColumnDataType = "nvarchar(200)", IsNullable = true)]
        public string ModifyUserName { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [EntityColumn(ColumnDescription = "修改时间", ColumnDataType = "datetime(3),datetime", IsNullable = true)]
        public DateTime? ModifyDate { get; set; }
    }
}
